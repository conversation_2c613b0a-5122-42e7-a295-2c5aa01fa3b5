package cn.leo.helper

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.impl.SimpleDataContext
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManagerListener
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.wm.ToolWindowManager
import java.awt.Component
import java.awt.event.MouseEvent
import javax.swing.JButton
import javax.swing.JComponent
import javax.swing.text.JTextComponent
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import kotlin.random.Random

class PluginStartupListener : ProjectManagerListener {

    private var scheduler: ScheduledExecutorService? = null
    private var currentProject: Project? = null
    private val logger = Logger.getInstance(PluginStartupListener::class.java)
    private val aiPluginDetector = AIPluginDetector()
    private val questionGenerator = AIQuestionGenerator()
    private val actionSelector = YTOAIActionSelector()
    private val config = KpiHelperConfig.getInstance()
    private val recentUsedActions = mutableListOf<String>()

    override fun projectOpened(project: Project) {
        if (scheduler != null && !scheduler!!.isShutdown) return

        currentProject = project

        // 检查是否启用
        if (!config.isEnabled()) {
            logger.info("KpiHelper: 插件已禁用，跳过启动")
            return
        }

        logger.info("KpiHelper: 项目已打开: ${project.name}，准备延迟初始化")

        // 延迟初始化，避免过早访问IDE服务
        ApplicationManager.getApplication().executeOnPooledThread {
            try {
                // 等待IDE完全初始化（进一步增加延迟时间）
                Thread.sleep(15000) // 等待15秒

                ApplicationManager.getApplication().invokeLater {
                    initializeScheduler(project)
                }
            } catch (e: Exception) {
                logger.error("KpiHelper: 延迟初始化失败", e)
            }
        }
    }

    private fun initializeScheduler(project: Project) {
        try {
            logger.info("KpiHelper: 开始初始化调度器")
            logger.info("KpiHelper: 当前配置 - enabled: ${config.enabled}, initialDelay: ${config.initialDelaySeconds}秒")

            if (scheduler != null && !scheduler!!.isShutdown) {
                logger.info("KpiHelper: 调度器已存在，跳过初始化")
                return
            }

            scheduler = Executors.newSingleThreadScheduledExecutor { r ->
                Thread(r, "KpiHelper-Scheduler").apply {
                    isDaemon = true
                }
            }

            // 首次执行
            val initialDelay = config.initialDelaySeconds.toLong()
            logger.info("KpiHelper: 安排首次执行，延迟: ${initialDelay}秒")
            scheduleNextExecution(initialDelay)
            logger.info("KpiHelper: AI助手自动化插件已启动，项目: ${project.name}")

        } catch (e: Exception) {
            logger.error("KpiHelper: 初始化调度器失败", e)
        }
    }

    override fun projectClosed(project: Project) {
        scheduler?.shutdownNow()
        scheduler = null
        currentProject = null
        logger.info("KpiHelper: AI助手自动化插件已停止")
    }

    private fun scheduleNextExecution(delaySeconds: Long) {
        val task = Runnable {
            try {
                logger.info("KpiHelper: 定时任务触发，开始检查执行条件")

                // 检查是否仍然启用
                if (!config.isEnabled()) {
                    logger.info("KpiHelper: 插件已禁用，停止执行")
                    return@Runnable
                }
                logger.info("KpiHelper: 插件已启用，继续检查")

                // 检查是否在工作时间（添加详细调试信息）
                val inWorkingHours = config.isInWorkingHours()
                logger.info("KpiHelper: 工作时间检查结果: $inWorkingHours")
                logger.info("KpiHelper: 启用工作时间限制: ${config.enableWorkingHoursOnly}")
                logger.info("KpiHelper: 启用工作日限制: ${config.enableWeekdaysOnly}")

                if (!inWorkingHours) {
                    val calendar = java.util.Calendar.getInstance()
                    val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
                    val dayOfWeek = calendar.get(java.util.Calendar.DAY_OF_WEEK)
                    logger.info("KpiHelper: 当前时间: ${hour}时, 星期: $dayOfWeek")
                    logger.info("KpiHelper: 工作时间: ${config.workingHoursStart}-${config.workingHoursEnd}")
                    logger.info("KpiHelper: 当前不在工作时间，跳过执行")
                    // 安排下次检查（较短间隔）
                    scheduleNextExecution(1800) // 30分钟 = 1800秒后再检查
                    return@Runnable
                }
                logger.info("KpiHelper: 在工作时间内，开始执行AI交互")

                // 检查是否需要用户确认
                if (config.requireUserConfirmation) {
                    logger.info("KpiHelper: 需要用户确认，显示确认对话框")
                    ApplicationManager.getApplication().invokeLater {
                        showScheduledExecutionConfirmation()
                    }
                } else {
                    logger.info("KpiHelper: 无需用户确认，直接执行")
                    executeAIInteraction()
                }
            } catch (e: Exception) {
                logger.error("KpiHelper: 执行AI交互时发生错误", e)
                config.incrementFailureCount()
            } finally {
                // 安排下次执行，使用随机间隔
                val nextDelay = config.getRandomInterval()
                logger.info("KpiHelper: 安排下次执行，间隔: $nextDelay 秒")
                scheduleNextExecution(nextDelay)
            }
        }

        if (scheduler == null || scheduler!!.isShutdown) {
            logger.warn("KpiHelper: 调度器不可用，无法安排任务")
            return
        }

        scheduler?.schedule(task, delaySeconds, TimeUnit.SECONDS)
        logger.info("KpiHelper: 已安排下次AI交互，将在 $delaySeconds 秒后执行")
    }

    /**
     * 显示定时执行确认对话框
     */
    private fun showScheduledExecutionConfirmation() {
        val project = currentProject ?: return

        val result = com.intellij.openapi.ui.Messages.showYesNoDialog(
            project,
            "AI助手自动化插件准备执行定时任务。\n\n是否继续执行？",
            "定时执行确认",
            "执行",
            "跳过",
            com.intellij.openapi.ui.Messages.getQuestionIcon()
        )

        if (result == com.intellij.openapi.ui.Messages.YES) {
            logger.info("KpiHelper: 用户确认执行定时任务")
            executeAIInteraction()
        } else {
            logger.info("KpiHelper: 用户取消执行定时任务")
        }
    }

    private fun executeAIInteraction() {
        val project = currentProject ?: return

        logger.info("KpiHelper: 开始执行AI交互...")
        config.incrementInteractionCount()

        // 优先使用配置中的首选AI插件
        var aiActionIds = config.preferredAIPlugins.toList()

        // 如果没有首选插件或启用了自动检测，则检测AI插件
        if (aiActionIds.isEmpty() || config.autoDetectAIPlugins) {
            val detectedActions = aiPluginDetector.detectAIPlugins()
            aiActionIds = if (config.preferredAIPlugins.isNotEmpty()) {
                // 合并首选和检测到的插件
                (config.preferredAIPlugins + detectedActions).distinct()
            } else {
                detectedActions
            }
        }

        // 添加自定义Action ID
        aiActionIds = (aiActionIds + config.customAIActionIds).distinct()
        
        if (aiActionIds.isEmpty()) {
            logger.warn("KpiHelper: 未检测到任何AI插件，尝试直接操作YtoAICode Tool Window")
            if (tryYtoAICodeToolWindow()) {
                return
            }
            logger.warn("KpiHelper: Tool Window方式失败，尝试使用预设的Action ID")
            tryPredefinedActions()
            return
        }

        // 从配置中获取用户选择的Action ID
        val userSelectedActions = config.customAIActionIds.filter { actionId ->
            aiActionIds.contains(actionId)
        }

        // 如果用户没有配置，使用检测到的Action
        val availableActions = if (userSelectedActions.isNotEmpty()) {
            userSelectedActions
        } else {
            aiActionIds
        }

        logger.info("KpiHelper: 可用Action数量: ${availableActions.size}")
        availableActions.forEach { actionId ->
            logger.info("KpiHelper: 可用Action: $actionId")
        }

        // 使用智能选择器选择最合适的Action
        val selectedActionId = actionSelector.selectActionByTimeAndUsage(availableActions, recentUsedActions)

        if (selectedActionId == null) {
            logger.warn("KpiHelper: Action选择器未能选择合适的Action")
            return
        }

        logger.info("KpiHelper: 智能选择AI插件: $selectedActionId")
        logger.info("KpiHelper: Action描述: ${actionSelector.getActionDescription(selectedActionId)}")

        // 记录使用历史
        recentUsedActions.add(selectedActionId)
        if (recentUsedActions.size > 10) {
            recentUsedActions.removeAt(0) // 保持最近10次的记录
        }

        // 生成问题
        val question = if (config.customQuestions.isNotEmpty() && Random.nextBoolean()) {
            // 50%概率使用自定义问题
            config.customQuestions.random()
        } else {
            questionGenerator.generateQuestion()
        }
        logger.info("KpiHelper: 生成问题: $question")

        // 使用新的YTO AI交互器直接执行选定的Action
        val interactor = YTOAIInteractor(project)
        val success = interactor.executeSpecificAction(selectedActionId, question)

        if (success) {
            config.incrementSuccessCount()
            logger.info("KpiHelper: YTO AI交互成功，Action: $selectedActionId")
        } else {
            config.incrementFailureCount()
            logger.warn("KpiHelper: YTO AI交互失败，Action: $selectedActionId")
        }
    }

    private fun executeAIAction(actionId: String, project: Project, question: String, retryCount: Int = 0) {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction(actionId)

        if (action == null) {
            logger.warn("KpiHelper: Action '$actionId' 未找到")
            if (retryCount < config.maxRetries) {
                // 尝试其他AI插件
                val alternativeActions = aiPluginDetector.detectAIPlugins().filter { it != actionId }
                if (alternativeActions.isNotEmpty()) {
                    executeAIAction(alternativeActions.random(), project, question, retryCount + 1)
                }
            }
            return
        }

        ApplicationManager.getApplication().invokeLater {
            try {
                val dataContext = SimpleDataContext.getProjectContext(project)
                val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.AutoInteraction", dataContext)

                action.actionPerformed(event)
                logger.info("KpiHelper: 成功触发AI插件动作: $actionId")
                config.incrementSuccessCount()

                // 模拟用户输入问题（如果启用）
                if (config.simulateUserInput) {
                    simulateUserInput(question)
                }

            } catch (e: Exception) {
                logger.error("KpiHelper: 执行AI动作时发生错误: $actionId", e)
                config.incrementFailureCount()

                if (retryCount < config.maxRetries) {
                    // 延迟重试
                    scheduler?.schedule({
                        executeAIAction(actionId, project, question, retryCount + 1)
                    }, 30, TimeUnit.SECONDS)
                }
            }
        }
    }

    private fun simulateUserInput(question: String) {
        // 这里可以尝试模拟键盘输入，但需要根据具体的AI插件来实现
        // 大多数AI插件会打开一个对话框或面板，我们可以尝试向其发送文本
        ApplicationManager.getApplication().executeOnPooledThread {
            try {
                // 使用配置的延迟时间，让AI插件界面完全加载
                Thread.sleep(config.inputDelaySeconds * 1000L)

                // 这里可以添加具体的文本输入逻辑
                // 例如使用Robot类或者直接操作组件
                logger.info("KpiHelper: 模拟用户输入: $question")

                // 可以尝试使用系统剪贴板和键盘事件
                // 但这需要根据具体的AI插件界面来实现

            } catch (e: Exception) {
                logger.warn("KpiHelper: 模拟用户输入失败", e)
            }
        }
    }

    private fun tryPredefinedActions() {
        // 尝试一些可能的YTO AI插件Action ID
        val possibleYTOActionIds = listOf(
            // YtoAICode Tool Window相关的Action ID
            "YtoAICode",
            "ActivateYtoAICodeToolWindow",
            "YtoAICode.ToolWindow",
            "YtoAICode.Show",
            "YtoAICode.Open",

            // 可能的YTO AI插件Action ID模式
            "yto.ai.chat",
            "yto.ai.assistant",
            "yto.aicode.chat",
            "ytoai.action.chat",
            "ytoai.action.ask",
            "ytoai.action.generate",
            "ai.chat.action",
            "ai.assistant.action",
            "chat.action",
            "ask.action",
            "generate.action",
            "explain.action"
        )

        val project = currentProject ?: return
        val question = questionGenerator.generateQuestion()

        logger.info("KpiHelper: 尝试预设的YTO AI插件Action ID")

        for (actionId in possibleYTOActionIds) {
            val actionManager = ActionManager.getInstance()
            val action = actionManager.getAction(actionId)
            if (action != null) {
                logger.info("KpiHelper: 找到YTO AI插件: $actionId")
                executeAIAction(actionId, project, question)
                return
            }
        }

        // 如果都没找到，尝试列出所有可用的Action来帮助调试
        listAllAvailableActions()
        logger.warn("KpiHelper: 未找到任何可用的YTO AI插件Action")
    }

    /**
     * 通过Tool Window方式触发YTO AI插件
     * 根据用户提供的信息：Tool Window ID: YtoAICode
     */
    private fun tryToolWindowApproach(project: Project, question: String) {
        try {
            logger.info("KpiHelper: 尝试通过Tool Window触发YTO AI插件")

            val toolWindowManager = ToolWindowManager.getInstance(project)
            val toolWindow = toolWindowManager.getToolWindow("YtoAICode")

            if (toolWindow == null) {
                logger.warn("KpiHelper: 未找到YtoAICode Tool Window")
                return
            }

            // 激活Tool Window
            ApplicationManager.getApplication().invokeLater {
                try {
                    toolWindow.activate {
                        logger.info("KpiHelper: YtoAICode Tool Window已激活")

                        // 延迟一下让Tool Window完全加载
                        ApplicationManager.getApplication().executeOnPooledThread {
                            try {
                                Thread.sleep(2000) // 等待2秒让界面加载完成

                                // 尝试模拟用户交互
                                simulateToolWindowInteraction(toolWindow, question)

                                config.incrementSuccessCount()
                                logger.info("KpiHelper: 通过Tool Window成功触发YTO AI插件")

                            } catch (e: Exception) {
                                logger.error("KpiHelper: Tool Window交互时发生错误", e)
                                config.incrementFailureCount()
                            }
                        }
                    }
                } catch (e: Exception) {
                    logger.error("KpiHelper: 激活Tool Window时发生错误", e)
                    config.incrementFailureCount()
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: Tool Window方式触发时发生错误", e)
            config.incrementFailureCount()
        }
    }

    /**
     * 模拟Tool Window中的用户交互
     */
    private fun simulateToolWindowInteraction(toolWindow: com.intellij.openapi.wm.ToolWindow, question: String) {
        try {
            val contentManager = toolWindow.contentManager
            val content = contentManager.selectedContent

            if (content != null) {
                val component = content.component
                logger.info("KpiHelper: 找到Tool Window内容组件: ${component.javaClass.simpleName}")

                // 尝试查找输入框和发送按钮
                findAndInteractWithComponents(component, question)
            } else {
                logger.warn("KpiHelper: Tool Window没有内容")
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 模拟Tool Window交互时发生错误", e)
        }
    }

    /**
     * 递归查找并与组件交互
     */
    private fun findAndInteractWithComponents(component: Component, question: String) {
        try {
            when (component) {
                is JComponent -> {
                    // 查找所有子组件
                    for (i in 0 until component.componentCount) {
                        val child = component.getComponent(i)
                        findAndInteractWithComponents(child, question)
                    }

                    // 如果是按钮，检查是否是发送按钮
                    if (component is JButton) {
                        val buttonText = component.text?.lowercase() ?: ""
                        val isSubmitButton = buttonText.contains("发送") ||
                                           buttonText.contains("send") ||
                                           buttonText.contains("submit") ||
                                           buttonText.contains("ask") ||
                                           buttonText.contains("提交")

                        if (isSubmitButton) {
                            logger.info("KpiHelper: 找到发送按钮: ${component.text}")

                            // 模拟点击发送按钮
                            ApplicationManager.getApplication().invokeLater {
                                try {
                                    component.doClick()
                                    logger.info("KpiHelper: 已点击发送按钮")
                                } catch (e: Exception) {
                                    logger.error("KpiHelper: 点击发送按钮时发生错误", e)
                                }
                            }
                        }
                    }

                    // 如果是文本输入组件，尝试输入问题
                    if (component is javax.swing.text.JTextComponent) {
                        logger.info("KpiHelper: 找到文本输入组件")

                        ApplicationManager.getApplication().invokeLater {
                            try {
                                component.text = question
                                logger.info("KpiHelper: 已输入问题: $question")
                            } catch (e: Exception) {
                                logger.error("KpiHelper: 输入问题时发生错误", e)
                            }
                        }
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 查找组件时发生错误", e)
        }
    }

    private fun tryYtoAICodeToolWindow(): Boolean {
        val project = currentProject ?: return false

        try {
            val toolWindowManager = ToolWindowManager.getInstance(project)
            val toolWindow = toolWindowManager.getToolWindow("YtoAICode")

            if (toolWindow != null) {
                logger.info("KpiHelper: 找到YtoAICode Tool Window")

                ApplicationManager.getApplication().invokeLater {
                    try {
                        // 激活Tool Window
                        toolWindow.activate(null)
                        logger.info("KpiHelper: 成功激活YtoAICode Tool Window")

                        // 等待Tool Window完全加载
                        Thread.sleep(1000)

                        // 尝试查找并点击聊天按钮
                        val chatButtonClicked = findAndClickChatButton(toolWindow.component)

                        if (chatButtonClicked) {
                            logger.info("KpiHelper: 成功点击聊天按钮")

                            // 生成问题并尝试输入
                            val question = questionGenerator.generateQuestion()
                            logger.info("KpiHelper: 生成问题: $question")

                            // 尝试输入问题（延迟执行）
                            Thread.sleep(1000)
                            simulateTextInput(question)

                        } else {
                            logger.warn("KpiHelper: 未找到聊天按钮，仅激活Tool Window")
                        }

                        // 更新统计
                        config.incrementInteractionCount()
                        config.incrementSuccessCount()

                    } catch (e: Exception) {
                        logger.error("KpiHelper: 激活YtoAICode Tool Window时发生错误", e)
                        config.incrementFailureCount()
                    }
                }

                return true
            } else {
                logger.warn("KpiHelper: 未找到YtoAICode Tool Window")
                return false
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 尝试操作YtoAICode Tool Window时发生错误", e)
            return false
        }
    }

    private fun findAndClickChatButton(component: JComponent): Boolean {
        try {
            // 递归查找所有按钮
            val buttons = findAllButtons(component)
            logger.info("KpiHelper: 找到 ${buttons.size} 个按钮")

            for (button in buttons) {
                // 检查按钮的文本、提示或其他属性
                val buttonText = button.text?.lowercase() ?: ""
                val toolTip = button.toolTipText?.lowercase() ?: ""
                val accessibleName = button.accessibleContext?.accessibleName?.lowercase() ?: ""

                logger.debug("KpiHelper: 检查按钮 - 文本: '$buttonText', 提示: '$toolTip', 名称: '$accessibleName'")

                // 查找可能是聊天/发送按钮的特征
                val isChatButton = buttonText.contains("发送") ||
                                 buttonText.contains("send") ||
                                 buttonText.contains("chat") ||
                                 buttonText.contains("聊天") ||
                                 buttonText.contains("提问") ||
                                 buttonText.contains("ask") ||
                                 toolTip.contains("发送") ||
                                 toolTip.contains("send") ||
                                 toolTip.contains("chat") ||
                                 toolTip.contains("聊天") ||
                                 accessibleName.contains("send") ||
                                 accessibleName.contains("chat")

                if (isChatButton && button.isEnabled && button.isVisible) {
                    logger.info("KpiHelper: 找到聊天按钮: $buttonText")

                    // 模拟点击
                    button.doClick()
                    return true
                }
            }

            // 如果没找到明确的聊天按钮，尝试点击第一个可用按钮
            val enabledButtons = buttons.filter { it.isEnabled && it.isVisible }
            if (enabledButtons.isNotEmpty()) {
                logger.info("KpiHelper: 未找到明确的聊天按钮，点击第一个可用按钮")
                enabledButtons.first().doClick()
                return true
            }

            return false

        } catch (e: Exception) {
            logger.error("KpiHelper: 查找聊天按钮时发生错误", e)
            return false
        }
    }

    private fun findAllButtons(component: Component): List<JButton> {
        val buttons = mutableListOf<JButton>()

        if (component is JButton) {
            buttons.add(component)
        }

        if (component is java.awt.Container) {
            for (child in component.components) {
                buttons.addAll(findAllButtons(child))
            }
        }

        return buttons
    }

    private fun simulateTextInput(text: String) {
        try {
            // 这里可以尝试多种文本输入方式

            // 方法1: 使用系统剪贴板
            val clipboard = java.awt.Toolkit.getDefaultToolkit().systemClipboard
            val stringSelection = java.awt.datatransfer.StringSelection(text)
            clipboard.setContents(stringSelection, null)

            // 方法2: 模拟键盘输入 Ctrl+V
            val robot = java.awt.Robot()
            robot.keyPress(java.awt.event.KeyEvent.VK_CONTROL)
            robot.keyPress(java.awt.event.KeyEvent.VK_V)
            robot.keyRelease(java.awt.event.KeyEvent.VK_V)
            robot.keyRelease(java.awt.event.KeyEvent.VK_CONTROL)

            Thread.sleep(500)

            // 模拟按回车发送
            robot.keyPress(java.awt.event.KeyEvent.VK_ENTER)
            robot.keyRelease(java.awt.event.KeyEvent.VK_ENTER)

            logger.info("KpiHelper: 成功模拟文本输入: $text")

        } catch (e: Exception) {
            logger.warn("KpiHelper: 模拟文本输入失败: $text", e)
        }
    }

    private fun listAllAvailableActions() {
        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            logger.info("KpiHelper: 开始列出所有可用的Action (总共${allActionIds.size}个)")

            // 只列出可能相关的Action
            val relevantActions = allActionIds.filter { actionId ->
                val actionIdLower = actionId.lowercase()
                actionIdLower.contains("ai") ||
                actionIdLower.contains("chat") ||
                actionIdLower.contains("assistant") ||
                actionIdLower.contains("yto") ||
                actionIdLower.contains("ask") ||
                actionIdLower.contains("generate") ||
                actionIdLower.contains("explain")
            }

            logger.info("KpiHelper: 找到 ${relevantActions.size} 个可能相关的Action:")
            relevantActions.forEach { actionId ->
                logger.info("KpiHelper: 可用Action: $actionId")
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 列出Action时发生错误", e)
        }
    }
}
