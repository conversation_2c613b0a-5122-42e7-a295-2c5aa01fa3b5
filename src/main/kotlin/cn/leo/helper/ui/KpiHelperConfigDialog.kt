package cn.leo.helper.ui

import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.*
import cn.leo.helper.KpiHelperConfig
import cn.leo.helper.YTOAIPluginHelper
import cn.leo.helper.YTOAIInteractor
import java.awt.*
import javax.swing.*

class KpiHelperConfigDialog(private val project: Project) : DialogWrapper(project) {
    
    private val config = KpiHelperConfig.getInstance()
    private val ytoHelper = YTOAIPluginHelper()
    
    // 基本设置组件
    private val enabledCheckBox = JBCheckBox("启用自动化", config.enabled)
    private val minIntervalSpinner = JSpinner(SpinnerNumberModel(config.minIntervalSeconds, 5, 86400, 5)) // 5秒到1天，步长5秒
    private val maxIntervalSpinner = JSpinner(SpinnerNumberModel(config.maxIntervalSeconds, 5, 86400, 5))
    private val initialDelaySpinner = JSpinner(SpinnerNumberModel(config.initialDelaySeconds, 0, 3600, 5)) // 0到1小时，步长5秒
    private val maxRetriesSpinner = JSpinner(SpinnerNumberModel(config.maxRetries, 0, 10, 1))
    
    // 时间限制组件
    private val workingHoursCheckBox = JBCheckBox("仅工作时间执行", config.enableWorkingHoursOnly)
    private val workingHoursStartSpinner = JSpinner(SpinnerNumberModel(config.workingHoursStart, 0, 23, 1))
    private val workingHoursEndSpinner = JSpinner(SpinnerNumberModel(config.workingHoursEnd, 0, 23, 1))
    private val weekdaysOnlyCheckBox = JBCheckBox("仅工作日执行", config.enableWeekdaysOnly)
    
    // AI插件设置组件
    private val autoDetectCheckBox = JBCheckBox("自动检测AI插件", config.autoDetectAIPlugins)
    private val customActionIdField = JBTextField()
    private val customActionIdsList = JBList<String>()
    private val customActionIdsModel = DefaultListModel<String>()
    
    // 问题生成组件
    private val randomQuestionsCheckBox = JBCheckBox("启用随机问题", config.enableRandomQuestions)
    private val customQuestionField = JBTextField()
    private val customQuestionsList = JBList<String>()
    private val customQuestionsModel = DefaultListModel<String>()
    
    // 执行设置组件
    private val simulateInputCheckBox = JBCheckBox("模拟用户输入", config.simulateUserInput)
    private val inputDelaySpinner = JSpinner(SpinnerNumberModel(config.inputDelaySeconds, 0, 10, 1))
    private val enableLoggingCheckBox = JBCheckBox("启用日志", config.enableLogging)

    // 聊天后关闭对话框组件
    private val autoCloseDialogCheckBox = JBCheckBox("聊天后自动关闭对话框", config.autoCloseDialogAfterChat)
    private val dialogCloseDelaySpinner = JSpinner(SpinnerNumberModel(config.dialogCloseDelaySeconds, 1, 30, 1))

    // 用户确认组件
    private val requireConfirmationCheckBox = JBCheckBox("执行前需要用户确认", config.requireUserConfirmation)
    
    // 状态显示组件
    private val statusLabel = JBLabel()
    private val statisticsLabel = JBLabel()
    
    init {
        title = "YTO AI助手自动化配置"
        setSize(600, 700)
        init()
        loadCurrentConfig()
        updateStatusDisplay()
    }
    
    override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout())
        val tabbedPane = JTabbedPane()

        // 基本设置标签页
        val basicPanel = createBasicSettingsPanel()
        tabbedPane.addTab("基本设置", basicPanel)

        // YTO AI插件设置标签页
        val aiPanel = createAIPluginSettingsPanel()
        tabbedPane.addTab("YTO AI插件", aiPanel)

        // 问题生成设置标签页
        val questionPanel = createQuestionSettingsPanel()
        tabbedPane.addTab("问题生成", questionPanel)

        // 状态显示标签页
        val statusPanel = createStatusPanel()
        tabbedPane.addTab("状态信息", statusPanel)

        mainPanel.add(tabbedPane, BorderLayout.CENTER)
        return mainPanel
    }

    private fun createBasicSettingsPanel(): JPanel {
        val panel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints()
        gbc.insets = Insets(5, 5, 5, 5)
        gbc.anchor = GridBagConstraints.WEST

        // 启用自动化
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2
        panel.add(enabledCheckBox, gbc)

        // 执行间隔
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 1
        panel.add(JLabel("最小间隔(秒):"), gbc)
        gbc.gridx = 1
        panel.add(minIntervalSpinner, gbc)

        gbc.gridx = 0; gbc.gridy = 2
        panel.add(JLabel("最大间隔(秒):"), gbc)
        gbc.gridx = 1
        panel.add(maxIntervalSpinner, gbc)

        // 初始延迟
        gbc.gridx = 0; gbc.gridy = 3
        panel.add(JLabel("初始延迟(秒):"), gbc)
        gbc.gridx = 1
        panel.add(initialDelaySpinner, gbc)

        // 最大重试次数
        gbc.gridx = 0; gbc.gridy = 4
        panel.add(JLabel("最大重试次数:"), gbc)
        gbc.gridx = 1
        panel.add(maxRetriesSpinner, gbc)

        // 时间限制
        gbc.gridx = 0; gbc.gridy = 5; gbc.gridwidth = 2
        panel.add(workingHoursCheckBox, gbc)

        gbc.gridx = 0; gbc.gridy = 6; gbc.gridwidth = 1
        panel.add(JLabel("工作开始时间:"), gbc)
        gbc.gridx = 1
        panel.add(workingHoursStartSpinner, gbc)

        gbc.gridx = 0; gbc.gridy = 7
        panel.add(JLabel("工作结束时间:"), gbc)
        gbc.gridx = 1
        panel.add(workingHoursEndSpinner, gbc)

        gbc.gridx = 0; gbc.gridy = 8; gbc.gridwidth = 2
        panel.add(weekdaysOnlyCheckBox, gbc)

        // 执行设置
        gbc.gridx = 0; gbc.gridy = 9; gbc.gridwidth = 2
        panel.add(simulateInputCheckBox, gbc)

        gbc.gridx = 0; gbc.gridy = 10; gbc.gridwidth = 1
        panel.add(JLabel("输入延迟(秒):"), gbc)
        gbc.gridx = 1
        panel.add(inputDelaySpinner, gbc)

        gbc.gridx = 0; gbc.gridy = 11; gbc.gridwidth = 2
        panel.add(enableLoggingCheckBox, gbc)

        // 聊天后关闭对话框设置
        gbc.gridx = 0; gbc.gridy = 12; gbc.gridwidth = 2
        panel.add(autoCloseDialogCheckBox, gbc)

        gbc.gridx = 0; gbc.gridy = 13; gbc.gridwidth = 1
        panel.add(JLabel("关闭延迟(秒):"), gbc)
        gbc.gridx = 1
        panel.add(dialogCloseDelaySpinner, gbc)

        // 用户确认设置
        gbc.gridx = 0; gbc.gridy = 14; gbc.gridwidth = 2
        panel.add(requireConfirmationCheckBox, gbc)

        return panel
    }

    private fun createAIPluginSettingsPanel(): JPanel {
        val panel = JPanel(BorderLayout())

        // 顶部控制面板
        val topPanel = JPanel(FlowLayout(FlowLayout.LEFT))
        topPanel.add(autoDetectCheckBox)

        val findButton = JButton("查找YTO AI插件")
        findButton.addActionListener { findYTOAIPlugin() }
        topPanel.add(findButton)

        val testButton = JButton("测试当前配置")
        testButton.addActionListener { testCurrentConfig() }
        topPanel.add(testButton)

        panel.add(topPanel, BorderLayout.NORTH)

        // 中间输入面板
        val inputPanel = JPanel(BorderLayout())
        val inputTopPanel = JPanel(FlowLayout(FlowLayout.LEFT))
        inputTopPanel.add(JLabel("自定义Action ID:"))
        inputTopPanel.add(customActionIdField)

        val addButton = JButton("添加")
        addButton.addActionListener { addCustomActionId() }
        inputTopPanel.add(addButton)

        inputPanel.add(inputTopPanel, BorderLayout.NORTH)

        // Action ID列表
        val listPanel = JPanel(BorderLayout())
        val scrollPane = JScrollPane(customActionIdsList)
        scrollPane.preferredSize = Dimension(400, 150)
        listPanel.add(scrollPane, BorderLayout.CENTER)

        val buttonPanel = JPanel(FlowLayout())
        val removeButton = JButton("删除选中")
        removeButton.addActionListener { removeSelectedActionId() }
        buttonPanel.add(removeButton)

        val restoreDefaultsButton = JButton("恢复默认Actions")
        restoreDefaultsButton.addActionListener { restoreDefaultActions() }
        buttonPanel.add(restoreDefaultsButton)

        listPanel.add(buttonPanel, BorderLayout.SOUTH)

        inputPanel.add(listPanel, BorderLayout.CENTER)
        panel.add(inputPanel, BorderLayout.CENTER)

        return panel
    }

    private fun createQuestionSettingsPanel(): JPanel {
        val panel = JPanel(BorderLayout())

        // 顶部控制面板
        val topPanel = JPanel(FlowLayout(FlowLayout.LEFT))
        topPanel.add(randomQuestionsCheckBox)
        panel.add(topPanel, BorderLayout.NORTH)

        // 中间输入面板
        val inputPanel = JPanel(BorderLayout())
        val inputTopPanel = JPanel(FlowLayout(FlowLayout.LEFT))
        inputTopPanel.add(JLabel("自定义问题:"))
        inputTopPanel.add(customQuestionField)

        val addButton = JButton("添加")
        addButton.addActionListener { addCustomQuestion() }
        inputTopPanel.add(addButton)

        inputPanel.add(inputTopPanel, BorderLayout.NORTH)

        // 问题列表
        val listPanel = JPanel(BorderLayout())
        val scrollPane = JScrollPane(customQuestionsList)
        scrollPane.preferredSize = Dimension(400, 200)
        listPanel.add(scrollPane, BorderLayout.CENTER)

        val removeButton = JButton("删除选中")
        removeButton.addActionListener { removeSelectedQuestion() }
        listPanel.add(removeButton, BorderLayout.SOUTH)

        inputPanel.add(listPanel, BorderLayout.CENTER)
        panel.add(inputPanel, BorderLayout.CENTER)

        return panel
    }

    private fun createStatusPanel(): JPanel {
        val panel = JPanel(BorderLayout())

        // 状态显示区域
        val statusPanel = JPanel(GridLayout(2, 1))
        statusPanel.add(statusLabel)
        statusPanel.add(statisticsLabel)
        panel.add(statusPanel, BorderLayout.NORTH)

        // 按钮面板
        val buttonPanel = JPanel(FlowLayout())
        val refreshButton = JButton("刷新状态")
        refreshButton.addActionListener { updateStatusDisplay() }
        buttonPanel.add(refreshButton)

        val resetButton = JButton("重置统计")
        resetButton.addActionListener { resetStatistics() }
        buttonPanel.add(resetButton)

        panel.add(buttonPanel, BorderLayout.CENTER)

        return panel
    }
    
    private fun loadCurrentConfig() {
        // 加载自定义Action ID列表
        customActionIdsModel.clear()

        // 首先添加建议的默认Action IDs（如果用户配置为空）
        if (config.customAIActionIds.isEmpty() && config.preferredAIPlugins.isEmpty()) {
            config.suggestedActionIds.forEach { actionId ->
                customActionIdsModel.addElement(actionId)
            }
        } else {
            // 加载用户配置的Action IDs
            val allActionIds = mutableSetOf<String>()
            allActionIds.addAll(config.customAIActionIds)
            allActionIds.addAll(config.preferredAIPlugins)

            // 确保建议的默认Action IDs也在列表中（用户可以选择删除）
            allActionIds.addAll(config.suggestedActionIds)

            allActionIds.forEach { actionId ->
                customActionIdsModel.addElement(actionId)
            }
        }

        customActionIdsList.model = customActionIdsModel

        // 加载自定义问题列表
        customQuestionsModel.clear()
        config.customQuestions.forEach { customQuestionsModel.addElement(it) }
        customQuestionsList.model = customQuestionsModel
    }
    
    private fun findYTOAIPlugin() {
        val foundActionId = ytoHelper.findYTOAIActionId()
        
        if (foundActionId != null) {
            customActionIdField.text = foundActionId
            JOptionPane.showMessageDialog(
                contentPanel,
                "找到YTO AI插件Action ID:\n$foundActionId\n\n点击'添加'按钮将其加入配置。",
                "找到插件",
                JOptionPane.INFORMATION_MESSAGE
            )
        } else {
            val allActions = ytoHelper.listAllPossibleActions()
            val message = if (allActions.isNotEmpty()) {
                "未自动找到YTO AI插件，但发现以下可能相关的Action:\n\n" +
                allActions.take(10).joinToString("\n") +
                if (allActions.size > 10) "\n... 还有 ${allActions.size - 10} 个" else ""
            } else {
                "未找到任何相关的Action。请确保YTO AI插件已正确安装并启用。"
            }
            
            JOptionPane.showMessageDialog(
                contentPanel,
                message,
                "查找结果",
                JOptionPane.WARNING_MESSAGE
            )
        }
    }
    
    private fun testCurrentConfig() {
        val actionIds = mutableListOf<String>()

        // 收集所有配置的Action ID
        for (i in 0 until customActionIdsModel.size()) {
            actionIds.add(customActionIdsModel.getElementAt(i))
        }

        if (actionIds.isEmpty()) {
            JOptionPane.showMessageDialog(
                contentPanel,
                "请先添加至少一个Action ID",
                "配置为空",
                JOptionPane.WARNING_MESSAGE
            )
            return
        }

        // 创建一个临时的YTOAIInteractor来测试Action可用性
        val project = com.intellij.openapi.project.ProjectManager.getInstance().openProjects.firstOrNull()
        if (project == null) {
            JOptionPane.showMessageDialog(
                contentPanel,
                "没有打开的项目，无法测试Action可用性",
                "无项目",
                JOptionPane.WARNING_MESSAGE
            )
            return
        }

        val results = mutableListOf<String>()

        try {
            val interactor = YTOAIInteractor(project)

            for (actionId in actionIds) {
                try {
                    val testResult = interactor.testActionAvailability(actionId)
                    results.add(testResult)
                } catch (e: Exception) {
                    results.add("❌ 测试Action '$actionId' 时发生错误: ${e.message}")
                }
            }
        } catch (e: Exception) {
            JOptionPane.showMessageDialog(
                contentPanel,
                "创建测试环境失败: ${e.message}",
                "测试失败",
                JOptionPane.ERROR_MESSAGE
            )
            return
        }

        // 创建一个可滚动的对话框来显示详细结果
        val textArea = javax.swing.JTextArea(results.joinToString("\n\n"))
        textArea.isEditable = false
        textArea.font = java.awt.Font(java.awt.Font.MONOSPACED, java.awt.Font.PLAIN, 12)

        val scrollPane = javax.swing.JScrollPane(textArea)
        scrollPane.preferredSize = java.awt.Dimension(600, 400)

        JOptionPane.showMessageDialog(
            contentPanel,
            scrollPane,
            "Action可用性测试结果",
            JOptionPane.INFORMATION_MESSAGE
        )
    }
    
    private fun addCustomActionId() {
        val actionId = customActionIdField.text.trim()
        if (actionId.isNotEmpty() && !customActionIdsModel.contains(actionId)) {
            customActionIdsModel.addElement(actionId)
            customActionIdField.text = ""
        }
    }
    
    private fun removeSelectedActionId() {
        val selectedIndex = customActionIdsList.selectedIndex
        if (selectedIndex >= 0) {
            customActionIdsModel.removeElementAt(selectedIndex)
        }
    }

    private fun restoreDefaultActions() {
        val result = JOptionPane.showConfirmDialog(
            contentPanel,
            "确定要恢复默认的Action配置吗？这将清除当前所有自定义Action并恢复为推荐的默认Actions。",
            "确认恢复默认",
            JOptionPane.YES_NO_OPTION
        )

        if (result == JOptionPane.YES_OPTION) {
            customActionIdsModel.clear()
            config.suggestedActionIds.forEach { actionId ->
                customActionIdsModel.addElement(actionId)
            }
            JOptionPane.showMessageDialog(
                contentPanel,
                "已恢复默认Action配置。包含以下Actions:\n\n" +
                config.suggestedActionIds.joinToString("\n"),
                "恢复完成",
                JOptionPane.INFORMATION_MESSAGE
            )
        }
    }
    
    private fun addCustomQuestion() {
        val question = customQuestionField.text.trim()
        if (question.isNotEmpty() && !customQuestionsModel.contains(question)) {
            customQuestionsModel.addElement(question)
            customQuestionField.text = ""
        }
    }
    
    private fun removeSelectedQuestion() {
        val selectedIndex = customQuestionsList.selectedIndex
        if (selectedIndex >= 0) {
            customQuestionsModel.removeElementAt(selectedIndex)
        }
    }
    
    private fun updateStatusDisplay() {
        val status = if (config.enabled) "已启用" else "已禁用"
        val workingHours = if (config.isInWorkingHours()) "是" else "否"
        statusLabel.text = "功能状态: $status | 当前在工作时间: $workingHours"
        
        val successRate = String.format("%.1f%%", config.getSuccessRate() * 100)
        statisticsLabel.text = "总执行: ${config.totalInteractions} | 成功: ${config.successfulInteractions} | 成功率: $successRate"
    }
    
    private fun resetStatistics() {
        val result = JOptionPane.showConfirmDialog(
            contentPanel,
            "确定要重置所有统计信息吗？",
            "确认重置",
            JOptionPane.YES_NO_OPTION
        )
        
        if (result == JOptionPane.YES_OPTION) {
            config.resetStatistics()
            updateStatusDisplay()
        }
    }
    
    override fun doOKAction() {
        // 保存配置
        saveConfig()
        super.doOKAction()
    }
    
    private fun saveConfig() {
        // 基本设置
        config.enabled = enabledCheckBox.isSelected
        config.minIntervalSeconds = minIntervalSpinner.value as Int
        config.maxIntervalSeconds = maxIntervalSpinner.value as Int
        config.initialDelaySeconds = initialDelaySpinner.value as Int
        config.maxRetries = maxRetriesSpinner.value as Int
        
        // 时间限制
        config.enableWorkingHoursOnly = workingHoursCheckBox.isSelected
        config.workingHoursStart = workingHoursStartSpinner.value as Int
        config.workingHoursEnd = workingHoursEndSpinner.value as Int
        config.enableWeekdaysOnly = weekdaysOnlyCheckBox.isSelected
        
        // AI插件设置
        config.autoDetectAIPlugins = autoDetectCheckBox.isSelected
        config.customAIActionIds.clear()
        config.preferredAIPlugins.clear()
        for (i in 0 until customActionIdsModel.size()) {
            val actionId = customActionIdsModel.getElementAt(i)
            config.customAIActionIds.add(actionId)
            config.preferredAIPlugins.add(actionId)
        }
        
        // 问题生成
        config.enableRandomQuestions = randomQuestionsCheckBox.isSelected
        config.customQuestions.clear()
        for (i in 0 until customQuestionsModel.size()) {
            config.customQuestions.add(customQuestionsModel.getElementAt(i))
        }
        
        // 执行设置
        config.simulateUserInput = simulateInputCheckBox.isSelected
        config.inputDelaySeconds = inputDelaySpinner.value as Int
        config.enableLogging = enableLoggingCheckBox.isSelected

        // 聊天后关闭对话框设置
        config.autoCloseDialogAfterChat = autoCloseDialogCheckBox.isSelected
        config.dialogCloseDelaySeconds = dialogCloseDelaySpinner.value as Int

        // 用户确认设置
        config.requireUserConfirmation = requireConfirmationCheckBox.isSelected
    }
}
