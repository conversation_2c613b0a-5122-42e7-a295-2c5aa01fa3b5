package cn.leo.helper.ui

import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import java.awt.*
import javax.swing.*

class ConfirmationDialog(
    private val project: Project,
    private val actionId: String,
    private val actionDescription: String,
    private val question: String?
) : DialogWrapper(project) {
    
    init {
        title = "确认执行YTO AI功能"
        setSize(500, 300)
        init()
    }
    
    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        
        // 主要信息面板
        val infoPanel = JPanel(GridBagLayout())
        val gbc = GridBagConstraints()
        gbc.insets = Insets(10, 10, 10, 10)
        gbc.anchor = GridBagConstraints.WEST
        
        // 标题
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2
        val titleLabel = JBLabel("即将执行YTO AI功能")
        titleLabel.font = titleLabel.font.deriveFont(Font.BOLD, 16f)
        infoPanel.add(titleLabel, gbc)
        
        // Action ID
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 1
        infoPanel.add(JBLabel("Action ID:"), gbc)
        gbc.gridx = 1
        val actionLabel = JBLabel(actionId)
        actionLabel.font = actionLabel.font.deriveFont(Font.BOLD)
        infoPanel.add(actionLabel, gbc)
        
        // 功能描述
        gbc.gridx = 0; gbc.gridy = 2
        infoPanel.add(JBLabel("功能描述:"), gbc)
        gbc.gridx = 1
        infoPanel.add(JBLabel(actionDescription), gbc)
        
        // 问题（如果有）
        if (!question.isNullOrEmpty()) {
            gbc.gridx = 0; gbc.gridy = 3
            infoPanel.add(JBLabel("输入问题:"), gbc)
            gbc.gridx = 1
            val questionArea = JTextArea(question)
            questionArea.isEditable = false
            questionArea.background = infoPanel.background
            questionArea.lineWrap = true
            questionArea.wrapStyleWord = true
            questionArea.rows = 3
            infoPanel.add(JScrollPane(questionArea), gbc)
        }
        
        panel.add(infoPanel, BorderLayout.CENTER)
        
        // 警告信息
        val warningPanel = JPanel(FlowLayout(FlowLayout.LEFT))
        val warningLabel = JBLabel("⚠️ 此操作将与YTO AI插件交互，可能会打断您当前的工作")
        warningLabel.foreground = Color.ORANGE
        warningPanel.add(warningLabel)
        panel.add(warningPanel, BorderLayout.SOUTH)
        
        return panel
    }
    
    override fun createActions(): Array<Action> {
        return arrayOf(okAction, cancelAction)
    }
    
    override fun getOKAction(): Action {
        val action = super.getOKAction()
        action.putValue(Action.NAME, "确认执行")
        return action
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }
}
