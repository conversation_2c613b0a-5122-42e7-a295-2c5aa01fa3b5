package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.wm.ToolWindowManager
import cn.leo.helper.KpiHelperConfig
import java.awt.Robot
import java.awt.event.KeyEvent
import java.awt.Toolkit
import java.awt.datatransfer.StringSelection

class TestToolWindowAction : AnAction() {
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            Messages.showErrorDialog("没有打开的项目", "错误")
            return
        }
        
        val config = KpiHelperConfig.getInstance()
        
        try {
            val toolWindowManager = ToolWindowManager.getInstance(project)
            val toolWindow = toolWindowManager.getToolWindow("YtoAICode")
            
            if (toolWindow == null) {
                Messages.showErrorDialog(
                    project,
                    "未找到YtoAICode Tool Window。\n\n请确保：\n1. YTO AI插件已安装\n2. 插件已启用\n3. Tool Window ID确实是'YtoAICode'",
                    "Tool Window未找到"
                )
                return
            }
            
            val message = buildString {
                appendLine("找到YTO AI Tool Window!")
                appendLine("Tool Window ID: YtoAICode")
                appendLine("是否可用: ${toolWindow.isAvailable}")
                appendLine("是否可见: ${toolWindow.isVisible}")
                appendLine("是否激活: ${toolWindow.isActive}")
                appendLine()
                appendLine("点击'是'将激活Tool Window并测试输入功能")
            }
            
            val result = Messages.showYesNoDialog(
                project,
                message,
                "找到YTO AI Tool Window",
                Messages.getQuestionIcon()
            )
            
            if (result == Messages.YES) {
                testToolWindowInteraction(toolWindow, project)
            }
            
        } catch (e: Exception) {
            Messages.showErrorDialog(
                project,
                "测试Tool Window时发生错误: ${e.message}",
                "测试失败"
            )
        }
    }
    
    private fun testToolWindowInteraction(toolWindow: com.intellij.openapi.wm.ToolWindow, project: com.intellij.openapi.project.Project) {
        ApplicationManager.getApplication().invokeLater {
            try {
                // 显示并激活Tool Window
                toolWindow.show {
                    toolWindow.activate {
                        Messages.showInfoMessage(
                            project,
                            "YTO AI Tool Window已激活！\n\n接下来将测试自动输入功能...",
                            "Tool Window已激活"
                        )
                        
                        // 延迟执行输入测试
                        ApplicationManager.getApplication().executeOnPooledThread {
                            try {
                                Thread.sleep(3000) // 等待3秒让用户看到消息
                                
                                val testQuestion = "请帮我检查这段代码的质量"
                                val success = testAutoInput(testQuestion)
                                
                                ApplicationManager.getApplication().invokeLater {
                                    if (success) {
                                        Messages.showInfoMessage(
                                            project,
                                            "自动输入测试成功！\n\n已输入测试问题: $testQuestion\n\n如果YTO AI插件响应了，说明自动化功能可以正常工作。",
                                            "测试成功"
                                        )
                                    } else {
                                        Messages.showWarningDialog(
                                            project,
                                            "自动输入测试失败。\n\n可能的原因：\n1. Tool Window界面结构与预期不同\n2. 需要手动点击输入框获得焦点\n3. YTO AI插件的界面已更新\n\n建议手动测试YTO AI插件是否正常工作。",
                                            "测试失败"
                                        )
                                    }
                                }
                                
                            } catch (e: Exception) {
                                ApplicationManager.getApplication().invokeLater {
                                    Messages.showErrorDialog(
                                        project,
                                        "自动输入测试时发生错误: ${e.message}",
                                        "测试错误"
                                    )
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Messages.showErrorDialog(
                    project,
                    "激活Tool Window时发生错误: ${e.message}",
                    "激活失败"
                )
            }
        }
    }
    
    private fun testAutoInput(question: String): Boolean {
        return try {
            // 方法1: 使用剪贴板输入
            val clipboard = Toolkit.getDefaultToolkit().systemClipboard
            val selection = StringSelection(question)
            clipboard.setContents(selection, null)
            
            Thread.sleep(500)
            
            // 使用Robot模拟键盘输入
            val robot = Robot()
            
            // 先尝试点击一下确保焦点在正确位置
            robot.keyPress(KeyEvent.VK_TAB)
            robot.keyRelease(KeyEvent.VK_TAB)
            Thread.sleep(200)
            
            // 粘贴内容
            robot.keyPress(KeyEvent.VK_CONTROL)
            robot.keyPress(KeyEvent.VK_V)
            Thread.sleep(100)
            robot.keyRelease(KeyEvent.VK_V)
            robot.keyRelease(KeyEvent.VK_CONTROL)
            
            Thread.sleep(500)
            
            // 按Enter发送
            robot.keyPress(KeyEvent.VK_ENTER)
            robot.keyRelease(KeyEvent.VK_ENTER)
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }
}
