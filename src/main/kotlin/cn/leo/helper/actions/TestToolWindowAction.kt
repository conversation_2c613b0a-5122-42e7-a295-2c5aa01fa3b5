package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.wm.ToolWindowManager
import cn.leo.helper.KpiHelperConfig
import java.awt.Robot
import java.awt.event.KeyEvent
import java.awt.Toolkit
import java.awt.datatransfer.StringSelection

class TestToolWindowAction : AnAction() {
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            Messages.showErrorDialog("没有打开的项目", "错误")
            return
        }
        
        val config = KpiHelperConfig.getInstance()
        
        try {
            val toolWindowManager = ToolWindowManager.getInstance(project)
            val toolWindow = toolWindowManager.getToolWindow("YtoAICode")
            
            if (toolWindow == null) {
                Messages.showErrorDialog(
                    project,
                    "未找到YtoAICode Tool Window。\n\n请确保：\n1. YTO AI插件已安装\n2. 插件已启用\n3. Tool Window ID确实是'YtoAICode'",
                    "Tool Window未找到"
                )
                return
            }
            
            val message = buildString {
                appendLine("找到YTO AI Tool Window!")
                appendLine("Tool Window ID: YtoAICode")
                appendLine("是否可用: ${toolWindow.isAvailable}")
                appendLine("是否可见: ${toolWindow.isVisible}")
                appendLine("是否激活: ${toolWindow.isActive}")
                appendLine()
                appendLine("点击'是'将激活Tool Window并测试输入功能")
            }
            
            val result = Messages.showYesNoDialog(
                project,
                message,
                "找到YTO AI Tool Window",
                Messages.getQuestionIcon()
            )
            
            if (result == Messages.YES) {
                testToolWindowInteraction(toolWindow, project)
            }
            
        } catch (e: Exception) {
            Messages.showErrorDialog(
                project,
                "测试Tool Window时发生错误: ${e.message}",
                "测试失败"
            )
        }
    }
    
    private fun testToolWindowInteraction(toolWindow: com.intellij.openapi.wm.ToolWindow, project: com.intellij.openapi.project.Project) {
        ApplicationManager.getApplication().invokeLater {
            try {
                val config = cn.leo.helper.KpiHelperConfig.getInstance()
                val helper = cn.leo.helper.YTOAIPluginHelper()
                val interactor = cn.leo.helper.YTOAIInteractor(project)

                // 获取用户自定义的第一个action，如果没有则使用NewChat
                val selectedAction = if (config.customAIActionIds.isNotEmpty()) {
                    config.customAIActionIds.first()
                } else {
                    "YtoAICode.NewChat"
                }

                // 根据Action类型生成合适的问题
                val generator = cn.leo.helper.AIQuestionGenerator()
                val testQuestion = if (helper.isChatAction(selectedAction)) {
                    "请帮我检查这段代码的质量"
                } else {
                    null
                }

                val actionDescription = helper.getActionDescription(selectedAction)
                val messageText = buildString {
                    appendLine("开始测试YTO AI功能...")
                    appendLine()
                    appendLine("将执行: $selectedAction")
                    appendLine("功能: $actionDescription")
                    if (!testQuestion.isNullOrEmpty()) {
                        appendLine("问题: $testQuestion")
                    }
                }

                Messages.showInfoMessage(project, messageText, "开始测试")

                // 延迟执行，让用户看到消息
                ApplicationManager.getApplication().executeOnPooledThread {
                    try {
                        Thread.sleep(2000) // 等待2秒

                        val success = interactor.executeSpecificAction(selectedAction, testQuestion)

                        ApplicationManager.getApplication().invokeLater {
                            if (success) {
                                val successMessage = buildString {
                                    appendLine("YTO AI功能测试成功！")
                                    appendLine()
                                    appendLine("已执行: $selectedAction")
                                    appendLine("功能: $actionDescription")
                                    if (!testQuestion.isNullOrEmpty()) {
                                        appendLine("问题: $testQuestion")
                                    }
                                    appendLine()
                                    if (helper.isChatAction(selectedAction)) {
                                        appendLine("如果聊天窗口打开并显示了问题，说明自动化功能正常工作。")
                                    } else {
                                        appendLine("如果代码处理功能正常执行，说明自动化功能正常工作。")
                                    }
                                }
                                Messages.showInfoMessage(project, successMessage, "测试成功")
                            } else {
                                val failureMessage = buildString {
                                    appendLine("YTO AI功能测试失败。")
                                    appendLine()
                                    appendLine("可能的原因：")
                                    appendLine("1. YTO AI插件未安装或未启用")
                                    appendLine("2. $selectedAction Action不可用")
                                    appendLine("3. 插件版本不兼容")
                                    if (helper.isCodeSelectionRequired(selectedAction)) {
                                        appendLine("4. 当前没有代码文件打开")
                                        appendLine("5. 需要先选中代码")
                                    }
                                    appendLine()
                                    appendLine("请检查YTO AI插件状态。")
                                }
                                Messages.showWarningDialog(project, failureMessage, "测试失败")
                            }
                        }

                    } catch (e: Exception) {
                        ApplicationManager.getApplication().invokeLater {
                            Messages.showErrorDialog(
                                project,
                                "测试YTO AI功能时发生错误: ${e.message}",
                                "测试错误"
                            )
                        }
                    }
                }

            } catch (e: Exception) {
                Messages.showErrorDialog(
                    project,
                    "启动测试时发生错误: ${e.message}",
                    "启动失败"
                )
            }
        }
    }
    

    
    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }
}
