package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.ui.Messages
import cn.leo.helper.AIQuestionGenerator
import cn.leo.helper.KpiHelperConfig
import cn.leo.helper.YTOAIInteractor
import cn.leo.helper.YTOAIActionSelector
import cn.leo.helper.YTOAIPluginHelper
import cn.leo.helper.ui.ConfirmationDialog
import com.intellij.openapi.diagnostic.Logger

class TriggerNowAction : AnAction() {
    
    private val logger = Logger.getInstance(TriggerNowAction::class.java)
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            Messages.showErrorDialog("没有打开的项目", "错误")
            return
        }
        
        val config = KpiHelperConfig.getInstance()
        
        try {
            val helper = YTOAIPluginHelper()

            // 获取用户自定义的第一个action，如果没有则使用NewChat
            val selectedAction = if (config.customAIActionIds.isNotEmpty()) {
                // 使用用户自定义的第一个action
                val firstAction = config.customAIActionIds.first()
                logger.info("KpiHelper: 使用用户自定义的第一个Action: $firstAction")
                firstAction
            } else {
                // 如果用户没有配置，使用默认的NewChat
                logger.info("KpiHelper: 用户未配置Action，使用默认的NewChat")
                "YtoAICode.NewChat"
            }

            // 验证选中的Action是否可用
            if (!helper.validateActionId(selectedAction)) {
                Messages.showWarningDialog(
                    project,
                    "Action '$selectedAction' 不可用。\n\n" +
                    "请确保：\n1. YTO AI插件已安装并启用\n2. 插件正常工作\n3. Action ID正确",
                    "Action不可用"
                )
                return
            }

            // 根据Action类型生成合适的问题
            val generator = AIQuestionGenerator()
            val question = if (helper.isChatAction(selectedAction)) {
                // 聊天类Action需要生成问题
                generator.generateQuestion()
            } else {
                // 代码处理类Action不需要问题
                null
            }

            // 显示确认对话框
            val actionDescription = helper.getActionDescription(selectedAction)
            val confirmDialog = ConfirmationDialog(
                project,
                selectedAction,
                actionDescription,
                question
            )

            if (!confirmDialog.showAndGet()) {
                logger.info("用户取消了YTO AI功能执行")
                return
            }

            // 使用YTO AI交互器
            val interactor = YTOAIInteractor(project)

            // 更新统计
            config.incrementInteractionCount()

            // 执行AI交互
            val success = interactor.executeSpecificAction(selectedAction, question)

            if (success) {
                config.incrementSuccessCount()
                val successMessage = buildString {
                    appendLine("已成功触发YTO AI功能！")
                    appendLine()
                    appendLine("Action: $selectedAction")
                    appendLine("功能: $actionDescription")
                    if (!question.isNullOrEmpty()) {
                        appendLine("问题: $question")
                    }
                }
                Messages.showInfoMessage(project, successMessage, "执行成功")
            } else {
                config.incrementFailureCount()
                val failureMessage = buildString {
                    appendLine("YTO AI功能执行失败。")
                    appendLine()
                    appendLine("Action: $selectedAction")
                    appendLine("功能: $actionDescription")
                    appendLine()
                    appendLine("请检查：")
                    appendLine("1. YTO AI插件是否正常工作")
                    if (helper.isCodeSelectionRequired(selectedAction)) {
                        appendLine("2. 当前是否有代码文件打开")
                        appendLine("3. 是否需要先选中代码")
                    }
                    appendLine("${if (helper.isCodeSelectionRequired(selectedAction)) "4" else "2"}. 查看日志获取详细错误信息")
                }
                Messages.showWarningDialog(project, failureMessage, "执行失败")
            }

        } catch (e: Exception) {
            logger.error("手动触发YTO AI交互时发生错误", e)
            config.incrementFailureCount()

            Messages.showErrorDialog(
                project,
                "执行YTO AI交互时发生错误: ${e.message}",
                "执行失败"
            )
        }
    }
    

    
    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }
}
