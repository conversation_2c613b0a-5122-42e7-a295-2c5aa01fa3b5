package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.ui.Messages
import cn.leo.helper.AIQuestionGenerator
import cn.leo.helper.KpiHelperConfig
import cn.leo.helper.YTOAIInteractor
import cn.leo.helper.YTOAIActionSelector
import cn.leo.helper.YTOAIPluginHelper
import cn.leo.helper.ui.ConfirmationDialog
import com.intellij.openapi.diagnostic.Logger

class TriggerNowAction : AnAction() {
    
    private val logger = Logger.getInstance(TriggerNowAction::class.java)
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            Messages.showErrorDialog("没有打开的项目", "错误")
            return
        }
        
        val config = KpiHelperConfig.getInstance()
        
        try {
            // 查找可用的YTO AI Action
            val helper = YTOAIPluginHelper()
            val detectedActions = helper.listAllPossibleActions()

            logger.info("检测到的Action: ${detectedActions.joinToString(", ")}")

            // 从配置中获取用户选择的Action
            val userSelectedActions = config.customAIActionIds.filter { actionId ->
                helper.validateActionId(actionId)
            }

            // 如果用户没有配置，使用检测到的Action
            val availableActions = if (userSelectedActions.isNotEmpty()) {
                userSelectedActions
            } else {
                detectedActions.filter { helper.validateActionId(it) }
            }

            if (availableActions.isEmpty()) {
                Messages.showWarningDialog(
                    project,
                    "未找到任何可用的YTO AI Action。\n\n" +
                    "检测到的Action: ${detectedActions.joinToString(", ")}\n\n" +
                    "请确保：\n1. YTO AI插件已安装并启用\n2. 插件正常工作\n3. 在配置中添加有效的Action ID",
                    "未找到Action"
                )
                return
            }

            // 使用Action选择器选择最佳Action
            val selector = YTOAIActionSelector()
            val selectedAction = selector.selectBestAction(availableActions, YTOAIActionSelector.UsageScenario.RANDOM)

            if (selectedAction == null) {
                Messages.showWarningDialog(
                    project,
                    "无法选择合适的YTO AI Action\n\n可用Action: ${availableActions.joinToString(", ")}",
                    "选择失败"
                )
                return
            }

            // 生成问题（仅对需要输入的Action）
            val generator = AIQuestionGenerator()
            val question = if (selector.getActionDescription(selectedAction).contains("聊天") ||
                             selector.getActionDescription(selectedAction).contains("问题")) {
                generator.generateQuestion()
            } else {
                null
            }

            // 显示确认对话框
            val confirmDialog = ConfirmationDialog(
                project,
                selectedAction,
                selector.getActionDescription(selectedAction),
                question
            )

            if (!confirmDialog.showAndGet()) {
                logger.info("用户取消了YTO AI功能执行")
                return
            }

            // 使用YTO AI交互器
            val interactor = YTOAIInteractor(project)

            // 更新统计
            config.incrementInteractionCount()

            // 执行AI交互
            val success = interactor.executeSpecificAction(selectedAction, question)

            if (success) {
                config.incrementSuccessCount()
                Messages.showInfoMessage(
                    project,
                    "已成功触发YTO AI功能！\n\n" +
                    "Action: $selectedAction\n" +
                    "功能: ${selector.getActionDescription(selectedAction)}\n" +
                    (if (!question.isNullOrEmpty()) "问题: $question" else ""),
                    "执行成功"
                )
            } else {
                config.incrementFailureCount()
                Messages.showWarningDialog(
                    project,
                    "YTO AI功能执行失败。\n\n" +
                    "Action: $selectedAction\n" +
                    "功能: ${selector.getActionDescription(selectedAction)}\n\n" +
                    "请检查：\n" +
                    "1. YTO AI插件是否正常工作\n" +
                    "2. 当前是否有代码文件打开\n" +
                    "3. 查看日志获取详细错误信息",
                    "执行失败"
                )
            }

        } catch (e: Exception) {
            logger.error("手动触发YTO AI交互时发生错误", e)
            config.incrementFailureCount()

            Messages.showErrorDialog(
                project,
                "执行YTO AI交互时发生错误: ${e.message}",
                "执行失败"
            )
        }
    }
    

    
    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }
}
