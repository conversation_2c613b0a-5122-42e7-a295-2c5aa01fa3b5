package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages
import cn.leo.helper.KpiHelperConfig
import cn.leo.helper.ui.KpiHelperConfigDialog

class OpenConfigAction : AnAction() {

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            Messages.showErrorDialog("没有打开的项目", "错误")
            return
        }

        // 打开新的配置对话框
        val dialog = KpiHelperConfigDialog(project)
        if (dialog.showAndGet()) {
            Messages.showInfoMessage(
                project,
                "配置已保存并生效",
                "配置成功"
            )
        }
    }

    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }

}
