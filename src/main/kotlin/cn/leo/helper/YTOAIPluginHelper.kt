package cn.leo.helper

import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.diagnostic.Logger

class YTOAIPluginHelper {

    private val logger = Logger.getInstance(YTOAIPluginHelper::class.java)

    /**
     * 获取YTO AI插件的默认Action ID
     * 用户明确指定插件名为YtoAICode，直接使用固定的Action列表
     */
    fun findYTOAIActionId(): String? {
        logger.info("KpiHelper: 获取YTO AI插件的默认Action ID")

        // 直接返回默认的聊天Action
        val defaultChatAction = "YtoAICode.NewChat"

        if (validateActionId(defaultChatAction)) {
            logger.info("KpiHelper: 找到YTO AI默认聊天Action: $defaultChatAction")
            return defaultChatAction
        }

        logger.warn("KpiHelper: YTO AI插件可能未安装或未启用")
        return null
    }

    /**
     * 获取YTO AI插件的所有已知Action列表
     * 这些是用户确认的YTO AI插件Action ID
     */
    fun getKnownYTOActions(): List<String> {
        return listOf(
            "YtoAICode.NewChat",           // 新建聊天
            "YtoAICode.AskQuestion",       // 询问问题
            "codegpt.FindBugs",            // 查找Bug
            "codegpt.WriteTests",          // 编写测试
            "codegpt.Explain",             // 解释代码
            "codegpt.Refactor",            // 重构代码
            "codegpt.Optimize",            // 优化代码
            "codegpt.创建新对话"            // 创建新对话
        )
    }



    /**
     * 列出YTO AI插件的所有Action，用于调试
     */
    fun listAllPossibleActions(): List<String> {
        logger.info("KpiHelper: 列出YTO AI插件的已知Actions")
        return getKnownYTOActions()
    }

    /**
     * 验证Action ID是否有效
     */
    fun validateActionId(actionId: String): Boolean {
        return try {
            val actionManager = ActionManager.getInstance()
            val action = actionManager.getAction(actionId)
            action != null
        } catch (e: Exception) {
            logger.error("KpiHelper: 验证Action ID时发生错误: $actionId", e)
            false
        }
    }

    /**
     * 获取推荐的Action执行顺序
     */
    fun getRecommendedActionSequence(actionId: String): List<String> {
        return when (actionId) {
            "YtoAICode.NewChat" -> {
                // 对于新建聊天：先打开聊天窗口，再创建新对话
                listOf("YtoAICode.NewChat", "codegpt.创建新对话")
            }
            "codegpt.Explain", "codegpt.FindBugs", "codegpt.WriteTests", "codegpt.Refactor", "codegpt.Optimize" -> {
                // 对于代码处理功能：需要先选中代码
                listOf(actionId) // 这些Action需要特殊处理（先选中代码）
            }
            "codegpt.创建新对话" -> {
                // 直接创建新对话
                listOf(actionId)
            }
            else -> {
                // 其他Action直接执行
                listOf(actionId)
            }
        }
    }

    /**
     * 判断Action是否需要选中代码
     */
    fun isCodeSelectionRequired(actionId: String): Boolean {
        val codeProcessingActions = listOf(
            "codegpt.Explain",
            "codegpt.FindBugs",
            "codegpt.WriteTests",
            "codegpt.Refactor",
            "codegpt.Optimize"
        )
        return codeProcessingActions.contains(actionId)
    }

    /**
     * 判断Action是否是聊天类型
     */
    fun isChatAction(actionId: String): Boolean {
        val chatActions = listOf(
            "YtoAICode.NewChat",
            "codegpt.创建新对话",
            "YtoAICode.AskQuestion"
        )
        return chatActions.contains(actionId)
    }

    /**
     * 获取Action的描述信息
     */
    fun getActionDescription(actionId: String): String {
        return when (actionId) {
            "YtoAICode.NewChat" -> "打开新的聊天窗口"
            "codegpt.创建新对话" -> "创建新的对话会话"
            "codegpt.Explain" -> "解释选中的代码（需要先选中代码）"
            "codegpt.FindBugs" -> "查找代码中的潜在问题（需要先选中代码）"
            "codegpt.WriteTests" -> "为选中的代码生成测试（需要先选中代码）"
            "codegpt.Refactor" -> "重构选中的代码（需要先选中代码）"
            "codegpt.Optimize" -> "优化选中的代码（需要先选中代码）"
            "YtoAICode.AskQuestion" -> "向AI提问"
            else -> "未知Action: $actionId"
        }
    }
}
