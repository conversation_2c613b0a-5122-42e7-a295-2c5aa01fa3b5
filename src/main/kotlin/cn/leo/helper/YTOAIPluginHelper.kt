package cn.leo.helper

import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.diagnostic.Logger

class YTOAIPluginHelper {

    private val logger = Logger.getInstance(YTOAIPluginHelper::class.java)

    /**
     * 查找YTO AI插件的Action ID
     * 根据用户提供的信息：Tool Window ID: YtoAICode, Plugin ID: ee.carlrobert.ytogpt
     */
    fun findYTOAIActionId(): String? {
        logger.info("KpiHelper: 开始查找YTO AI插件的Action ID")
        logger.info("KpiHelper: 目标插件 - Tool Window ID: YtoAICode, Plugin ID: ee.carlrobert.ytogpt")

        // 策略1: 直接查找已知的YTO AI插件
        val knownYTOActions = findKnownYTOActions()
        if (knownYTOActions.isNotEmpty()) {
            val selectedAction = knownYTOActions.first()
            logger.info("KpiHelper: 找到已知的YTO AI Action: $selectedAction")
            return selectedAction
        }

        // 策略2: 根据Plugin ID查找相关Action
        val pluginActions = findActionsByPluginId("ee.carlrobert.ytogpt")
        if (pluginActions.isNotEmpty()) {
            val selectedAction = pluginActions.first()
            logger.info("KpiHelper: 通过Plugin ID找到YTO AI Action: $selectedAction")
            return selectedAction
        }

        // 策略3: 查找Tool Window相关的Action
        val toolWindowActions = findToolWindowActions("YtoAICode")
        if (toolWindowActions.isNotEmpty()) {
            val selectedAction = toolWindowActions.first()
            logger.info("KpiHelper: 通过Tool Window找到YTO AI Action: $selectedAction")
            return selectedAction
        }

        // 策略4: 扫描所有Action，查找YTO相关的
        val ytoActions = scanForYTOActions()
        if (ytoActions.isNotEmpty()) {
            val selectedAction = ytoActions.first()
            logger.info("KpiHelper: 通过扫描找到YTO AI Action: $selectedAction")
            return selectedAction
        }

        logger.warn("KpiHelper: 未找到任何YTO AI插件的Action ID")
        return null
    }

    /**
     * 查找已知的YTO AI插件Action
     * 根据用户提供的右键菜单截图，这些是实际存在的Action ID
     */
    private fun findKnownYTOActions(): List<String> {
        val knownActions = listOf(
            // 用户截图中确认存在的Action ID
            "YtoAICode.NewChat",           // 新建聊天
            "YtoAICode.AskQuestion",       // 询问问题
            "codegpt.FindBugs",            // 查找Bug
            "codegpt.WriteTests",          // 编写测试
            "codegpt.Explain",             // 解释代码
            "codegpt.Refactor",            // 重构代码
            "codegpt.Optimize",            // 优化代码

            // 可能的其他相关Action
            "YtoAICode.ExplainCode",
            "YtoAICode.GenerateCode",
            "YtoAICode.ReviewCode",
            "YtoAICode.OptimizeCode",
            "YtoAICode.RefactorCode",
            "YtoAICode.WriteTests",
            "YtoAICode.FindBugs",

            // CodeGPT相关（从截图看YTO AI可能基于CodeGPT）
            "codegpt.GenerateCode",
            "codegpt.ReviewCode",
            "codegpt.AskQuestion",
            "codegpt.NewChat"
        )

        val foundActions = mutableListOf<String>()
        val actionManager = ActionManager.getInstance()

        for (actionId in knownActions) {
            val action = actionManager.getAction(actionId)
            if (action != null) {
                foundActions.add(actionId)
                logger.info("KpiHelper: 验证成功的YTO AI Action: $actionId")
            }
        }

        return foundActions
    }

    /**
     * 根据Plugin ID查找相关Action
     */
    private fun findActionsByPluginId(pluginId: String): List<String> {
        val actions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            for (actionId in allActionIds) {
                if (actionId.contains(pluginId) || actionId.contains("ytogpt") || actionId.contains("carlrobert")) {
                    val action = actionManager.getAction(actionId)
                    if (action != null && isInteractiveAction(actionId)) {
                        actions.add(actionId)
                        logger.info("KpiHelper: 通过Plugin ID找到Action: $actionId")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 根据Plugin ID查找Action时发生错误", e)
        }

        return actions
    }

    /**
     * 查找Tool Window相关的Action
     */
    private fun findToolWindowActions(toolWindowId: String): List<String> {
        val actions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            for (actionId in allActionIds) {
                if (actionId.contains(toolWindowId) || actionId.lowercase().contains("ytoaicode")) {
                    val action = actionManager.getAction(actionId)
                    if (action != null && isInteractiveAction(actionId)) {
                        actions.add(actionId)
                        logger.info("KpiHelper: 通过Tool Window找到Action: $actionId")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 根据Tool Window查找Action时发生错误", e)
        }

        return actions
    }

    /**
     * 检查已安装的插件，查找YTO相关的插件
     */
    private fun findYTOPluginActions(): List<String> {
        val actions = mutableListOf<String>()

        try {
            val installedPlugins = PluginManagerCore.plugins

            for (plugin in installedPlugins.toList()) {
                if (!plugin.isEnabled) continue

                val pluginId = plugin.pluginId.idString.lowercase()
                val pluginName = plugin.name.lowercase()

                // 检查是否是YTO相关的插件
                val isYTOPlugin = pluginId.contains("yto") ||
                        pluginName.contains("yto") ||
                        pluginId.contains("aicode") ||
                        pluginName.contains("aicode")

                if (isYTOPlugin) {
                    logger.info("KpiHelper: 发现YTO插件: ${plugin.name} (${plugin.pluginId})")

                    // 查找该插件的Action
                    val pluginActions = findActionsForPlugin(plugin.pluginId.idString)
                    actions.addAll(pluginActions)
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 查找YTO插件时发生错误", e)
        }

        return actions
    }

    /**
     * 扫描所有Action，查找YTO相关的
     */
    private fun scanForYTOActions(): List<String> {
        val actions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            val ytoKeywords = listOf("ytoaicode", "YtoAICode", "yto", "ytoai", "aicode")

            for (actionId in allActionIds) {
                val actionIdLower = actionId.lowercase()

                val isYTOAction = ytoKeywords.any { keyword ->
                    actionIdLower.contains(keyword)
                }

                if (isYTOAction) {
                    val action = actionManager.getAction(actionId)
                    if (action != null) {
                        actions.add(actionId)
                        logger.info("KpiHelper: 发现YTO Action: $actionId")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 扫描YTO Action时发生错误", e)
        }

        return actions
    }

    /**
     * 查找通用的AI相关Action
     */
    private fun scanForGenericAIActions(): List<String> {
        val actions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            val aiKeywords = listOf("ai.chat", "ai.assistant", "chat", "assistant")

            for (actionId in allActionIds) {
                val actionIdLower = actionId.lowercase()

                val isAIAction = aiKeywords.any { keyword ->
                    actionIdLower.contains(keyword)
                }

                if (isAIAction) {
                    val action = actionManager.getAction(actionId)
                    if (action != null && isInteractiveAction(actionId)) {
                        actions.add(actionId)
                        logger.info("KpiHelper: 发现通用AI Action: $actionId")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 扫描通用AI Action时发生错误", e)
        }

        return actions
    }

    /**
     * 查找特定插件的Action
     */
    private fun findActionsForPlugin(pluginId: String): List<String> {
        val actions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            for (actionId in allActionIds) {
                // 检查Action ID是否属于该插件
                if (actionId.startsWith(pluginId) ||
                    actionId.contains(pluginId.split(".").last())
                ) {

                    val action = actionManager.getAction(actionId)
                    if (action != null && isInteractiveAction(actionId)) {
                        actions.add(actionId)
                        logger.info("KpiHelper: 插件 $pluginId 的Action: $actionId")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 查找插件 $pluginId 的Action时发生错误", e)
        }

        return actions
    }

    /**
     * 判断是否是有用的YTO AI Action（包括聊天和代码处理功能）
     */
    private fun isInteractiveAction(actionId: String): Boolean {
        val actionIdLower = actionId.lowercase()

        // YTO AI和CodeGPT的有用功能关键词
        val usefulKeywords = listOf(
            // 聊天和问答功能
            "chat", "ask", "question", "newchat", "openchat",

            // 代码处理功能（这些都是有价值的AI使用）
            "explain", "generate", "refactor", "optimize", "review",
            "findbugs", "writetests", "analyze", "improve", "complete",
            "suggest", "fix", "debug", "format"
        )

        // 排除管理和配置相关的Action
        val excludeKeywords = listOf(
            "settings", "config", "preference", "install", "update",
            "disable", "enable", "uninstall", "about", "help", "login",
            "logout", "register", "account", "subscription", "license"
        )

        // 包含有用关键词
        val hasUsefulKeyword = usefulKeywords.any { keyword ->
            actionIdLower.contains(keyword)
        }

        // 不包含排除关键词
        val hasExcludeKeyword = excludeKeywords.any { keyword ->
            actionIdLower.contains(keyword)
        }

        // 特殊处理：直接匹配已知的YTO AI Action
        val knownYTOActions = listOf(
            "ytoaicode.newchat", "ytoaicode.askquestion", "ytoaicode.explain",
            "codegpt.findbugs", "codegpt.writetests", "codegpt.explain",
            "codegpt.refactor", "codegpt.optimize", "codegpt.generate"
        )

        val isKnownYTOAction = knownYTOActions.any { known ->
            actionIdLower.contains(known.replace(".", ""))
        }

        return (hasUsefulKeyword || isKnownYTOAction) && !hasExcludeKeyword
    }

    /**
     * 列出所有可能相关的Action，用于调试
     */
    fun listAllPossibleActions(): List<String> {
        val relevantActions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            logger.info("KpiHelper: 列出所有可能相关的Action (总共${allActionIds.size}个)")

            for (actionId in allActionIds) {
                val actionIdLower = actionId.lowercase()

                val isRelevant = actionIdLower.contains("yto") ||
                        actionIdLower.contains("ai") ||
                        actionIdLower.contains("chat") ||
                        actionIdLower.contains("assistant") ||
                        actionIdLower.contains("ask") ||
                        actionIdLower.contains("generate") ||
                        actionIdLower.contains("explain") ||
                        actionIdLower.contains("complete")

                if (isRelevant) {
                    relevantActions.add(actionId)
                    logger.info("KpiHelper: 相关Action: $actionId")
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 列出Action时发生错误", e)
        }

        return relevantActions
    }

    /**
     * 验证Action ID是否有效
     */
    fun validateActionId(actionId: String): Boolean {
        return try {
            val actionManager = ActionManager.getInstance()
            val action = actionManager.getAction(actionId)
            action != null
        } catch (e: Exception) {
            logger.error("KpiHelper: 验证Action ID时发生错误: $actionId", e)
            false
        }
    }
}
