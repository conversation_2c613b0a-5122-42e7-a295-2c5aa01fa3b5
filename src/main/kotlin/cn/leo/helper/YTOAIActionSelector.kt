package cn.leo.helper

import com.intellij.openapi.diagnostic.Logger
import kotlin.random.Random

/**
 * YTO AI Action选择器
 * 根据不同场景智能选择合适的Action
 */
class YTOAIActionSelector {
    
    private val logger = Logger.getInstance(YTOAIActionSelector::class.java)
    
    // 根据用户截图确认的Action ID
    private val confirmedActions = listOf(
        "YtoAICode.NewChat",
        "YtoAICode.AskQuestion",
        "codegpt.FindBugs",
        "codegpt.WriteTests",
        "codegpt.Explain",
        "codegpt.Refactor",
        "codegpt.Optimize",
        "codegpt.创建新对话"
    )

    // Action分类
    private val chatActions = listOf(
        "YtoAICode.NewChat",
        "YtoAICode.AskQuestion",
        "codegpt.创建新对话"
    )
    
    private val codeAnalysisActions = listOf(
        "codegpt.Explain",
        "codegpt.FindBugs",
        "codegpt.Refactor",
        "codegpt.Optimize"
    )
    
    private val testingActions = listOf(
        "codegpt.WriteTests"
    )
    
    /**
     * 根据可用的Action和使用场景选择最合适的Action
     */
    fun selectBestAction(availableActions: List<String>, scenario: UsageScenario = UsageScenario.RANDOM): String? {
        logger.info("KpiHelper: 选择Action，可用数量: ${availableActions.size}, 场景: $scenario")
        
        if (availableActions.isEmpty()) {
            logger.warn("KpiHelper: 没有可用的Action")
            return null
        }
        
        // 过滤出确认存在的Action
        val validActions = availableActions.filter { action ->
            confirmedActions.any { confirmed -> 
                action.equals(confirmed, ignoreCase = true) 
            }
        }
        
        if (validActions.isEmpty()) {
            logger.info("KpiHelper: 没有找到确认的Action，使用所有可用Action")
            return availableActions.random()
        }
        
        logger.info("KpiHelper: 找到 ${validActions.size} 个确认的Action: $validActions")
        
        return when (scenario) {
            UsageScenario.CHAT -> selectChatAction(validActions)
            UsageScenario.CODE_ANALYSIS -> selectCodeAnalysisAction(validActions)
            UsageScenario.TESTING -> selectTestingAction(validActions)
            UsageScenario.RANDOM -> selectRandomAction(validActions)
        }
    }
    
    /**
     * 选择聊天相关的Action
     */
    private fun selectChatAction(availableActions: List<String>): String? {
        val chatActionsAvailable = availableActions.filter { action ->
            chatActions.any { chat -> action.equals(chat, ignoreCase = true) }
        }
        
        return if (chatActionsAvailable.isNotEmpty()) {
            val selected = chatActionsAvailable.random()
            logger.info("KpiHelper: 选择聊天Action: $selected")
            selected
        } else {
            logger.info("KpiHelper: 没有聊天Action，选择随机Action")
            availableActions.random()
        }
    }
    
    /**
     * 选择代码分析相关的Action
     */
    private fun selectCodeAnalysisAction(availableActions: List<String>): String? {
        val analysisActionsAvailable = availableActions.filter { action ->
            codeAnalysisActions.any { analysis -> action.equals(analysis, ignoreCase = true) }
        }
        
        return if (analysisActionsAvailable.isNotEmpty()) {
            val selected = analysisActionsAvailable.random()
            logger.info("KpiHelper: 选择代码分析Action: $selected")
            selected
        } else {
            logger.info("KpiHelper: 没有代码分析Action，选择随机Action")
            availableActions.random()
        }
    }
    
    /**
     * 选择测试相关的Action
     */
    private fun selectTestingAction(availableActions: List<String>): String? {
        val testingActionsAvailable = availableActions.filter { action ->
            testingActions.any { test -> action.equals(test, ignoreCase = true) }
        }
        
        return if (testingActionsAvailable.isNotEmpty()) {
            val selected = testingActionsAvailable.random()
            logger.info("KpiHelper: 选择测试Action: $selected")
            selected
        } else {
            logger.info("KpiHelper: 没有测试Action，选择随机Action")
            availableActions.random()
        }
    }
    
    /**
     * 随机选择Action，但有权重偏好
     */
    private fun selectRandomAction(availableActions: List<String>): String {
        // 定义Action权重（越高越容易被选中）
        val actionWeights = mapOf(
            "YtoAICode.NewChat" to 3,        // 聊天功能权重高
            "YtoAICode.AskQuestion" to 3,    // 问答功能权重高
            "codegpt.Explain" to 2,          // 解释代码权重中等
            "codegpt.FindBugs" to 2,         // 查找Bug权重中等
            "codegpt.Refactor" to 1,         // 重构权重较低
            "codegpt.Optimize" to 1,         // 优化权重较低
            "codegpt.WriteTests" to 1        // 写测试权重较低
        )
        
        // 创建加权列表
        val weightedActions = mutableListOf<String>()
        for (action in availableActions) {
            val weight = actionWeights[action] ?: 1
            repeat(weight) {
                weightedActions.add(action)
            }
        }
        
        val selected = weightedActions.random()
        logger.info("KpiHelper: 随机选择Action: $selected")
        return selected
    }
    
    /**
     * 获取Action的描述信息
     */
    fun getActionDescription(actionId: String): String {
        return when (actionId) {
            "YtoAICode.NewChat" -> "开始新的AI聊天对话"
            "YtoAICode.AskQuestion" -> "向AI询问问题"
            "codegpt.FindBugs" -> "使用AI查找代码中的潜在Bug（需要先选中代码）"
            "codegpt.WriteTests" -> "使用AI生成单元测试（需要先选中代码）"
            "codegpt.Explain" -> "使用AI解释代码功能（需要先选中代码）"
            "codegpt.Refactor" -> "使用AI重构代码（需要先选中代码）"
            "codegpt.Optimize" -> "使用AI优化代码性能（需要先选中代码）"
            "codegpt.创建新对话" -> "创建新的AI对话（自动聚焦输入框）"
            else -> "执行AI功能: $actionId"
        }
    }
    
    /**
     * 根据时间和使用频率选择Action
     */
    fun selectActionByTimeAndUsage(availableActions: List<String>, lastUsedActions: List<String>): String? {
        if (availableActions.isEmpty()) return null
        
        // 过滤出最近没有使用过的Action
        val recentlyUsed = lastUsedActions.takeLast(3) // 最近3次使用的Action
        val lessUsedActions = availableActions.filter { action ->
            !recentlyUsed.contains(action)
        }
        
        // 如果有未最近使用的Action，优先选择
        val candidateActions = if (lessUsedActions.isNotEmpty()) {
            lessUsedActions
        } else {
            availableActions
        }
        
        // 根据时间选择不同类型的Action
        val hour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        
        return when {
            hour in 9..11 -> {
                // 上午：偏向代码分析和Bug查找
                selectCodeAnalysisAction(candidateActions) ?: candidateActions.random()
            }
            hour in 14..16 -> {
                // 下午：偏向聊天和问答
                selectChatAction(candidateActions) ?: candidateActions.random()
            }
            else -> {
                // 其他时间：随机选择
                selectRandomAction(candidateActions)
            }
        }
    }
    
    /**
     * 使用场景枚举
     */
    enum class UsageScenario {
        CHAT,           // 聊天场景
        CODE_ANALYSIS,  // 代码分析场景
        TESTING,        // 测试场景
        RANDOM          // 随机场景
    }
}
