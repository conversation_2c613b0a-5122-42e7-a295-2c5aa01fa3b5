package cn.leo.helper

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.*
import com.intellij.util.xmlb.XmlSerializerUtil

@State(
    name = "KpiHelperConfig",
    storages = [Storage("kpi-helper-config.xml")]
)
@Service
class KpiHelperConfig : PersistentStateComponent<KpiHelperConfig> {
    
    // 基本配置
    var enabled: Boolean = true
    var minIntervalSeconds: Int = 3600  // 60分钟 = 3600秒
    var maxIntervalSeconds: Int = 10800 // 180分钟 = 10800秒
    var initialDelaySeconds: Int = 300  // 5分钟 = 300秒
    var maxRetries: Int = 3
    
    // 问题生成配置
    var enableRandomQuestions: Boolean = true
    var questionTypes: MutableList<String> = mutableListOf(
        "PROGRAMMING", "KOTLIN", "JAVA", "ARCHITECTURE", 
        "DEBUGGING", "CODE_REVIEW", "LEARNING"
    )
    
    // 自定义问题
    var customQuestions: MutableList<String> = mutableListOf(
        "请帮我检查这段代码的质量",
        "这个方法有什么可以优化的地方？",
        "如何改进这段代码的可读性？",
        "这段代码有什么潜在的问题吗？",
        "如何优化这个算法的性能？"
    )
    
    // AI插件配置 - 简化为只包含YtoAICode插件的Actions
    var preferredAIPlugins: MutableList<String> = mutableListOf()
    var customAIActionIds: MutableList<String> = mutableListOf()

    // 建议的默认Action ID（用户明确指定的YtoAICode插件Actions）
    val suggestedActionIds: List<String> = listOf(
        "YtoAICode.NewChat",        // 新建聊天
        "YtoAICode.AskQuestion",    // 询问问题
        "codegpt.FindBugs",         // 查找Bug
        "codegpt.WriteTests",       // 编写测试
        "codegpt.Explain",          // 解释代码
        "codegpt.Refactor",         // 重构代码
        "codegpt.Optimize",         // 优化代码
        "codegpt.创建新对话"         // 创建新对话
    )
    var autoDetectAIPlugins: Boolean = true
    var useToolWindow: Boolean = true  // 优先使用Tool Window方式
    
    // 执行配置
    var simulateUserInput: Boolean = true
    var inputDelaySeconds: Int = 2
    var enableLogging: Boolean = true
    var logLevel: String = "INFO"

    // 聊天后关闭对话框配置
    var autoCloseDialogAfterChat: Boolean = false
    var dialogCloseDelaySeconds: Int = 5
    
    // 工作时间配置
    var enableWorkingHoursOnly: Boolean = false
    var workingHoursStart: Int = 9  // 9:00
    var workingHoursEnd: Int = 18   // 18:00
    var enableWeekdaysOnly: Boolean = false
    
    // 统计信息
    var totalInteractions: Int = 0
    var lastInteractionTime: Long = 0
    var successfulInteractions: Int = 0
    var failedInteractions: Int = 0
    
    override fun getState(): KpiHelperConfig = this
    
    override fun loadState(state: KpiHelperConfig) {
        XmlSerializerUtil.copyBean(state, this)
    }
    
    companion object {
        fun getInstance(): KpiHelperConfig {
            return ApplicationManager.getApplication().getService(KpiHelperConfig::class.java)
        }
    }
    
    // 便利方法
    fun isInWorkingHours(): Boolean {
        if (!enableWorkingHoursOnly) return true
        
        val calendar = java.util.Calendar.getInstance()
        val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
        val dayOfWeek = calendar.get(java.util.Calendar.DAY_OF_WEEK)
        
        val isWorkingDay = if (enableWeekdaysOnly) {
            dayOfWeek in java.util.Calendar.MONDAY..java.util.Calendar.FRIDAY
        } else true
        
        val isWorkingHour = hour in workingHoursStart until workingHoursEnd
        
        return isWorkingDay && isWorkingHour
    }
    
    fun incrementInteractionCount() {
        totalInteractions++
        lastInteractionTime = System.currentTimeMillis()
    }
    
    fun incrementSuccessCount() {
        successfulInteractions++
    }
    
    fun incrementFailureCount() {
        failedInteractions++
    }
    
    fun getSuccessRate(): Double {
        return if (totalInteractions > 0) {
            successfulInteractions.toDouble() / totalInteractions.toDouble()
        } else 0.0
    }
    
    fun resetStatistics() {
        totalInteractions = 0
        successfulInteractions = 0
        failedInteractions = 0
        lastInteractionTime = 0
    }
    
    fun addCustomQuestion(question: String) {
        if (question.isNotBlank() && !customQuestions.contains(question)) {
            customQuestions.add(question)
        }
    }
    
    fun removeCustomQuestion(question: String) {
        customQuestions.remove(question)
    }
    
    fun addPreferredAIPlugin(actionId: String) {
        if (actionId.isNotBlank() && !preferredAIPlugins.contains(actionId)) {
            preferredAIPlugins.add(actionId)
        }
    }
    
    fun removePreferredAIPlugin(actionId: String) {
        preferredAIPlugins.remove(actionId)
    }
    
    fun getAllQuestions(): List<String> {
        val allQuestions = mutableListOf<String>()
        
        if (enableRandomQuestions) {
            val generator = AIQuestionGenerator()
            // 从每个启用的类型中获取一些问题
            questionTypes.forEach { typeStr ->
                try {
                    val type = AIQuestionGenerator.QuestionType.valueOf(typeStr)
                    allQuestions.add(generator.generateSpecificQuestion(type))
                } catch (e: IllegalArgumentException) {
                    // 忽略无效的问题类型
                }
            }
        }
        
        allQuestions.addAll(customQuestions)
        return allQuestions
    }
    
    fun getRandomInterval(): Long {
        return if (minIntervalMinutes >= maxIntervalMinutes) {
            minIntervalMinutes.toLong()
        } else {
            kotlin.random.Random.nextLong(minIntervalMinutes.toLong(), maxIntervalMinutes.toLong())
        }
    }
    
    fun isEnabled(): Boolean = enabled

    fun shouldLog(): Boolean = enableLogging

    fun getCurrentLogLevel(): String = logLevel
    
    // 验证配置的有效性
    fun validateConfig(): List<String> {
        val errors = mutableListOf<String>()
        
        if (minIntervalMinutes <= 0) {
            errors.add("最小间隔时间必须大于0")
        }
        
        if (maxIntervalMinutes <= 0) {
            errors.add("最大间隔时间必须大于0")
        }
        
        if (minIntervalMinutes > maxIntervalMinutes) {
            errors.add("最小间隔时间不能大于最大间隔时间")
        }
        
        if (initialDelayMinutes < 0) {
            errors.add("初始延迟时间不能为负数")
        }
        
        if (maxRetries < 0) {
            errors.add("最大重试次数不能为负数")
        }
        
        if (workingHoursStart < 0 || workingHoursStart > 23) {
            errors.add("工作开始时间必须在0-23之间")
        }
        
        if (workingHoursEnd < 0 || workingHoursEnd > 23) {
            errors.add("工作结束时间必须在0-23之间")
        }
        
        if (workingHoursStart >= workingHoursEnd) {
            errors.add("工作开始时间必须早于结束时间")
        }
        
        return errors
    }
    
    // 重置为默认配置
    fun resetToDefaults() {
        enabled = true
        minIntervalMinutes = 60
        maxIntervalMinutes = 180
        initialDelayMinutes = 5
        maxRetries = 3
        
        enableRandomQuestions = true
        questionTypes.clear()
        questionTypes.addAll(listOf(
            "PROGRAMMING", "KOTLIN", "JAVA", "ARCHITECTURE", 
            "DEBUGGING", "CODE_REVIEW", "LEARNING"
        ))
        
        customQuestions.clear()
        customQuestions.addAll(listOf(
            "请帮我检查这段代码的质量",
            "这个方法有什么可以优化的地方？",
            "如何改进这段代码的可读性？",
            "这段代码有什么潜在的问题吗？",
            "如何优化这个算法的性能？"
        ))

        preferredAIPlugins.clear()
        preferredAIPlugins.addAll(suggestedActionIds)

        customAIActionIds.clear()
        customAIActionIds.addAll(suggestedActionIds)

        autoDetectAIPlugins = true
        useToolWindow = true
        
        simulateUserInput = true
        inputDelaySeconds = 2
        enableLogging = true
        logLevel = "INFO"
        
        enableWorkingHoursOnly = false
        workingHoursStart = 9
        workingHoursEnd = 18
        enableWeekdaysOnly = false
    }
}
