# YTO AI助手自动化 - 快速开始

## 简介

这个插件专门为自动化使用公司的YTO AI编程助手而设计，帮助您满足公司的AI工具使用KPI要求。

## 已知信息

根据您的调试发现：
- **Tool Window ID**: `YtoAICode`
- **Plugin ID**: `ee.carlrobert.ytogpt`
- **可用Action ID**:
  - `YtoAICode.NewChat` - 新建聊天
  - `YtoAICode.AskQuestion` - 询问问题
  - `codegpt.FindBugs` - 查找Bug
  - `codegpt.WriteTests` - 编写测试
  - `codegpt.Explain` - 解释代码
  - `codegpt.Refactor` - 重构代码
  - `codegpt.Optimize` - 优化代码

## 快速设置（3步完成）

### 第1步：启动插件
```bash
./gradlew runIde
```

### 第2步：配置YTO AI插件
1. 打开菜单：**Tools → AI助手自动化 → 配置设置**
2. 点击"查找YTO AI插件"按钮（应该能找到上述Action ID）
3. 如果找到Action ID，点击"添加"保存
4. 点击"测试当前配置"验证是否工作
5. **新功能**：点击"测试Tool Window交互"测试Tool Window功能

### 第3步：启动自动化
1. 点击菜单：**Tools → AI助手自动化 → 启用/禁用自动化**
2. 或者点击"立即触发一次"进行测试

## 配置建议

### 基本设置
```
启用自动化: ✓
执行间隔: 90-150分钟（推荐）
工作时间限制: ✓ (9:00-18:00)
仅工作日: ✓
```

### 问题设置
```
启用随机问题: ✓
自定义问题: 添加一些与您工作相关的问题
```

## 验证是否工作

### 方法1：手动测试
1. 点击"立即触发一次"
2. 观察是否打开YTO AI插件
3. 查看是否自动输入问题

### 方法2：查看日志
查看IDE日志，应该看到：
```
KpiHelper: 找到YTO AI插件Action: xxx
KpiHelper: YtoAICode Tool Window已激活
KpiHelper: 通过Tool Window成功触发YTO AI插件
```

### 方法3：查看统计
点击"查看状态"菜单，查看执行统计信息。

## 故障排除

### 问题1：找不到YTO AI插件
**解决方案**：
1. 确认YTO AI插件已安装并启用
2. 重启IDE
3. 手动添加Action ID到配置中

### 问题2：无法自动输入问题
**解决方案**：
1. 检查"模拟用户输入"是否启用
2. 调整"输入延迟"时间
3. 尝试不同的交互策略

### 问题3：执行失败率高
**解决方案**：
1. 增加执行间隔时间
2. 检查YTO AI插件是否正常工作
3. 查看详细错误日志

## 高级功能

### 动态配置
- 所有配置都可以实时修改
- 无需重启IDE即可生效
- 支持导入/导出配置

### 多策略交互
插件会自动尝试多种方式与YTO AI插件交互：
1. **Action方式**: 直接调用插件的Action
2. **Tool Window方式**: 激活工具窗口并模拟用户操作
3. **键盘快捷键方式**: 使用快捷键触发

### 智能问题生成
- 200+内置编程问题
- 支持多种编程语言
- 可添加自定义问题
- 根据文件类型生成相关问题

## 使用技巧

### 1. 合理设置执行频率
```
# 满足基本要求
间隔: 120-180分钟，每天3-4次

# 积极使用
间隔: 60-90分钟，每天6-8次
```

### 2. 自定义问题示例
```
请帮我检查这段代码的质量
这个方法有什么可以优化的地方？
如何改进这段代码的可读性？
这段代码遵循了最佳实践吗？
请解释这个算法的时间复杂度
```

### 3. 监控和调整
- 定期查看执行统计
- 根据成功率调整配置
- 关注YTO AI插件的响应情况

## 注意事项

1. **合规使用**: 确保符合公司AI工具使用政策
2. **适度频率**: 避免过于频繁的执行
3. **监控效果**: 定期检查自动化是否正常工作
4. **保持更新**: YTO AI插件更新后可能需要重新配置

## 菜单功能说明

### Tools → AI助手自动化

- **启用/禁用自动化**: 快速开关自动化功能
- **立即触发一次**: 手动测试，查看效果
- **配置设置**: 打开完整的配置界面
- **查看状态**: 查看运行状态和统计信息
- **查找YTO AI插件**: 自动查找并配置YTO AI插件

## 成功指标

### 正常工作的标志
- 成功率 > 80%
- 每天执行次数符合预期
- YTO AI插件能正常响应
- 日志中无严重错误

### 需要调整的情况
- 成功率 < 50%
- 频繁出现错误日志
- YTO AI插件无响应
- 执行次数不符合预期

通过这个自动化工具，您可以轻松满足公司对YTO AI工具使用的要求，同时不会干扰正常的开发工作。如果遇到问题，请查看详细的错误日志或尝试手动测试功能。
