# 执行间隔时间单位修改总结

## 🔄 修改内容

### 1. 配置类修改 (KpiHelperConfig.kt)
- **minIntervalMinutes** → **minIntervalSeconds** (默认值: 3600秒 = 60分钟)
- **maxIntervalMinutes** → **maxIntervalSeconds** (默认值: 10800秒 = 180分钟)
- **initialDelayMinutes** → **initialDelaySeconds** (默认值: 300秒 = 5分钟)
- 更新了相关的验证和重置方法

### 2. 调度器修改 (PluginStartupListener.kt)
- **scheduleNextExecution(delayMinutes)** → **scheduleNextExecution(delaySeconds)**
- 使用 **TimeUnit.SECONDS** 替代 **TimeUnit.MINUTES**
- 更新了日志输出，显示秒数而不是分钟数
- 工作时间检查间隔：30分钟 = 1800秒

### 3. 配置界面修改 (KpiHelperConfigDialog.kt)
- 标签更新：
  - "最小间隔(分钟)" → "最小间隔(秒)"
  - "最大间隔(分钟)" → "最大间隔(秒)"
  - "初始延迟(分钟)" → "初始延迟(秒)"
- Spinner范围调整：
  - 间隔时间：60-86400秒 (1分钟到1天)，步长60秒
  - 初始延迟：0-3600秒 (0到1小时)，步长30秒

### 4. 状态显示修改 (ShowStatusAction.kt)
- 同时显示分钟和秒数，便于用户理解
- 格式：`60-180 分钟 (3600-10800 秒)`

### 5. Plugin.xml描述更新
添加了详细的插件使用说明，包括：
- **主要功能**：智能调度、多种操作、问题生成等
- **快速使用**：5步快速上手指南
- **配置说明**：详细的配置项说明
- **支持的YTO AI功能**：列出所有支持的Action

## 📊 时间单位对照表

| 原分钟值 | 新秒数值 | 说明 |
|---------|---------|------|
| 1分钟 | 60秒 | 最小间隔下限 |
| 5分钟 | 300秒 | 默认初始延迟 |
| 30分钟 | 1800秒 | 工作时间检查间隔 |
| 60分钟 | 3600秒 | 默认最小执行间隔 |
| 180分钟 | 10800秒 | 默认最大执行间隔 |
| 1440分钟 | 86400秒 | 最大间隔上限(1天) |

## 🎯 用户体验改进

### 配置界面
- **更精确的控制**：用户可以设置精确到秒的执行间隔
- **合理的步长**：间隔时间步长为60秒(1分钟)，初始延迟步长为30秒
- **清晰的范围**：明确的最小值和最大值限制

### 状态显示
- **双重显示**：同时显示分钟和秒数，便于理解
- **实时反馈**：准确显示下次执行时间(秒级精度)

### Plugin描述
- **完整指南**：从安装到配置的完整使用指南
- **功能列表**：详细列出所有支持的YTO AI功能
- **配置说明**：每个配置项的详细说明

## 🔧 技术改进

### 精度提升
- 从分钟级精度提升到秒级精度
- 支持更灵活的执行间隔设置

### 兼容性
- 保持了原有的功能逻辑
- 只是改变了时间单位，不影响核心功能

### 可维护性
- 统一了时间单位，减少了单位转换的复杂性
- 更清晰的变量命名

## 📝 注意事项

1. **配置迁移**：现有用户的配置会自动使用新的默认值
2. **日志格式**：日志中的时间显示已更新为秒数
3. **向后兼容**：核心功能保持不变，只是时间精度提升

## 🚀 使用建议

### 推荐配置
- **开发环境**：最小间隔3600秒(1小时)，最大间隔7200秒(2小时)
- **测试环境**：最小间隔1800秒(30分钟)，最大间隔3600秒(1小时)
- **生产环境**：最小间隔7200秒(2小时)，最大间隔14400秒(4小时)

### 快速测试
- 初始延迟：60秒(1分钟)
- 最小间隔：300秒(5分钟)
- 最大间隔：600秒(10分钟)
