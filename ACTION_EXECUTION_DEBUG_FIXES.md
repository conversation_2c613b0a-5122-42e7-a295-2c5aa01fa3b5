# Action执行问题修复和调试

## 🐛 发现的问题

### 1. NewChat的Tab键问题
- **问题**：Tab键输入了8次，太多了
- **修复**：减少到3次，并增加间隔时间到300ms

### 2. 其他Action没有执行
- **问题**：代码处理Action只是选中代码后就没有动作了
- **可能原因**：
  - Action不存在
  - Action不可用（需要特定条件）
  - DataContext不正确
  - 执行时机问题

## 🔧 修复措施

### 1. Tab键优化
```kotlin
// 旧代码：8次Tab键，间隔200ms
for (i in 1..8) {
    robot.keyPress(KeyEvent.VK_TAB)
    robot.keyRelease(KeyEvent.VK_TAB)
    Thread.sleep(200)
}

// 新代码：3次Tab键，间隔300ms
for (i in 1..3) {
    robot.keyPress(KeyEvent.VK_TAB)
    robot.keyRelease(KeyEvent.VK_TAB)
    Thread.sleep(300)
}
```

### 2. 改进DataContext
- **问题**：简单的ProjectContext可能不够
- **修复**：创建包含编辑器信息的DataContext
```kotlin
private fun getCurrentEditorDataContext(): DataContext? {
    val fileEditorManager = FileEditorManager.getInstance(project)
    val selectedEditor = fileEditorManager.selectedEditor
    
    if (selectedEditor != null) {
        val dataContextBuilder = SimpleDataContext.builder()
        dataContextBuilder.add(CommonDataKeys.PROJECT, project)
        dataContextBuilder.add(CommonDataKeys.EDITOR, editor)
        dataContextBuilder.add(CommonDataKeys.VIRTUAL_FILE, selectedEditor.file)
        return dataContextBuilder.build()
    }
    return SimpleDataContext.getProjectContext(project)
}
```

### 3. 增强Action状态检查
```kotlin
// 检查Action是否可用
action.update(event)
if (!event.presentation.isEnabled) {
    logger.warn("Action '$actionId' 当前不可用，原因: ${event.presentation.text}")
    logger.warn("Action可见性: ${event.presentation.isVisible}")
    logger.warn("Action描述: ${event.presentation.description}")
    return
}
```

### 4. 改进执行时序
```kotlin
// 确保在UI线程中执行Action
ApplicationManager.getApplication().invokeLater {
    try {
        executeActionDirectly(actionId)
    } catch (e: Exception) {
        logger.error("执行Action失败: $actionId", e)
    }
}
```

## 🔍 新增调试功能

### 1. Action可用性测试方法
```kotlin
fun testActionAvailability(actionId: String): String {
    val actionManager = ActionManager.getInstance()
    val action = actionManager.getAction(actionId)
    
    if (action == null) {
        return "❌ Action '$actionId' 不存在"
    }
    
    val dataContext = getCurrentEditorDataContext() ?: SimpleDataContext.getProjectContext(project)
    val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.Test", dataContext)
    
    action.update(event)
    
    return buildString {
        appendLine("✅ Action '$actionId' 存在")
        appendLine("   - 可用: ${event.presentation.isEnabled}")
        appendLine("   - 可见: ${event.presentation.isVisible}")
        appendLine("   - 文本: ${event.presentation.text}")
        appendLine("   - 描述: ${event.presentation.description}")
        appendLine("   - Action类: ${action.javaClass.simpleName}")
    }
}
```

### 2. 配置界面测试功能
- 在配置界面的"测试当前配置"按钮中集成了Action可用性测试
- 显示详细的Action状态信息
- 可滚动的对话框显示完整结果

## 🧪 调试步骤

### 1. 使用配置界面测试
1. 打开配置界面：Tools → AI助手自动化 → 配置设置
2. 确保有配置的Action ID
3. 点击"测试当前配置"按钮
4. 查看详细的Action状态报告

### 2. 查看日志输出
关键日志信息：
```
KpiHelper: 识别为代码处理Action: codegpt.Explain
KpiHelper: 找到编辑器: MyFile.java
KpiHelper: Action 'codegpt.Explain' 可用，准备执行
KpiHelper: 成功直接执行Action: codegpt.Explain
```

如果Action不可用，会显示：
```
KpiHelper: Action 'codegpt.Explain' 当前不可用，原因: Explain Code
KpiHelper: Action可见性: true
KpiHelper: Action描述: Explain the selected code
```

### 3. 检查前置条件
对于代码处理Action，确保：
- ✅ 有打开的代码文件
- ✅ 代码文件有内容
- ✅ YTO AI插件已安装并启用
- ✅ 编辑器获得了焦点

## 🎯 可能的问题和解决方案

### 问题1：Action不存在
**症状**：日志显示"Action 'xxx' 未找到"
**解决**：
- 检查Action ID是否正确
- 确认YTO AI插件已安装
- 验证插件版本兼容性

### 问题2：Action不可用
**症状**：Action存在但isEnabled=false
**可能原因**：
- 没有选中代码（对于代码处理Action）
- 没有打开的编辑器
- 编辑器类型不支持
- 插件状态异常

**解决**：
- 确保有代码文件打开
- 确保代码已选中
- 重启IDE
- 重新安装YTO AI插件

### 问题3：DataContext问题
**症状**：Action执行但没有效果
**解决**：
- 使用包含编辑器信息的DataContext
- 确保编辑器获得焦点
- 检查文件类型支持

## 📋 测试清单

### NewChat功能测试
- [ ] 聊天窗口是否正常打开
- [ ] Tab键次数是否合理（3次）
- [ ] 问题是否正确输入
- [ ] 自动关闭功能是否工作

### 代码处理功能测试
- [ ] 代码是否正确选中
- [ ] Action是否存在且可用
- [ ] Action是否正确执行
- [ ] 是否有预期的效果

### 配置界面测试
- [ ] Action可用性测试是否显示详细信息
- [ ] 所有配置的Action状态是否正确
- [ ] 错误信息是否有帮助

## 🚀 下一步建议

1. **重新构建插件**并测试
2. **使用配置界面的测试功能**检查Action状态
3. **查看IDE日志**了解详细执行情况
4. **逐个测试Action**确定哪些可用
5. **根据测试结果**进一步调整代码

这些修复应该能帮助诊断和解决Action执行问题！
